package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class BatimentCheckResponse(
    @SerialName("CLI_Code")
    val cliCode: String,


  //  @SerialName("Clt_ImoCB")
   // val cltImoCB: String,

    @SerialName("code")
    val code: Int,
    @SerialName("message")
    val message: String
)
