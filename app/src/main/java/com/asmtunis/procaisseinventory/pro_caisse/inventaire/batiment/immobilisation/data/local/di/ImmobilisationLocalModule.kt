package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.local.dao.ImmobilisationDAO
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.local.repository.ImmobilisationLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.local.repository.ImmobilisationLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
class ImmobilisationLocalModule {

    @Provides
    @Singleton
    fun provideImmobilisationDao(
        proCaisseDataBase: ProCaisseDataBase
    ) = proCaisseDataBase.immobilisationDao()

    @Provides
    @Singleton
    @Named("Immobilisation")
    fun provideImmobilisationRepository(
        immobilisationDAO: ImmobilisationDAO
    ): ImmobilisationLocalRepository = ImmobilisationLocalRepositoryImpl(immobilisationDAO = immobilisationDAO)
}