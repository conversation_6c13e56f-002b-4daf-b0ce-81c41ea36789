package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.view_model

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.text.input.TextFieldValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.core.UiText
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.Constant.ZONE_CONSOMATION
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.Immobilisation
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.zone_consomation.ZoneConsommationListState
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.ListEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.ListOrder
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.OrderType
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.launch
import javax.inject.Inject


@HiltViewModel
class InventaireBatimentViewModel @Inject constructor(
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
    private val proCaisseRemote: ProCaisseRemote,
    private val proCaisseLocalDb: ProCaisseLocalDb
) : ViewModel() {


   /* var showAffectationCBDialogue: Boolean by mutableStateOf(false)
        private set



    private fun onShowAffectationCBDialoguChange(value: Boolean) {
        showAffectationCBDialogue = value
    }*/


    var displayAffectedBatiment by mutableStateOf(true)
        private set
    fun onDisplayAffectedBatimentChange(value: Boolean) {
        displayAffectedBatiment = value
    }

    var tyEmpImNom by mutableStateOf(ZONE_CONSOMATION)
        private set
    fun onTyEmpImNomChange(value: String) {
        tyEmpImNom = value
    }


    var selectedSocite by mutableStateOf(Immobilisation())
        private set
    fun onSelectedSociteChange(value: Immobilisation) {
        selectedSocite = value
    }

    var selectedSiteFinancier by mutableStateOf(Immobilisation())
        private set
    fun onSelectedSiteFinancierChange(value: Immobilisation) {
        selectedSiteFinancier = value
    }


    var showAddCBBatiment by mutableStateOf(false)
        private set
    fun onshowAddCBBatimentChange(value: Boolean) {
        showAddCBBatiment = value
    }


    var selectedZoneConsomation  by mutableStateOf(Immobilisation())
        private set
    fun onSelectedZoneConsomationChange(value: Immobilisation, from: String) {
        selectedZoneConsomation = value
    }


   
    var zoneConsomationCB by mutableStateOf("")
        private set
    fun onZoneConsomationCBChange(value: String, from: String) {
        zoneConsomationCB = value
    }

    var zoneConsomationErrorCB: UiText? by mutableStateOf(null)
        private set
    fun onZoneConsomationErrorCBChange(value: UiText?) {
        zoneConsomationErrorCB = value
    }


    var showCustomFilter  by mutableStateOf(false)
        private set
    fun onShowCustomFilterChange(value: Boolean) {
        showCustomFilter = value
    }



    var immobilisationTreeList = mutableStateListOf<Immobilisation>()
        private set

    fun resetImmobilisationTreeList() {
        immobilisationTreeList.clear()
    }


    fun resetSearch() {
       onSearchValueChange(TextFieldValue(""))
        onShowSearchViewChange(false)
       onShowCustomFilterChange(false)
    }



    fun getParentImobilisation(codeParent : String) {
        viewModelScope.launch {
            proCaisseLocalDb.immobilisation.getParent(codeParent = codeParent).collect {
               if(it == null) return@collect
                immobilisationTreeList.add(it)
                    if(!it.cliImoCodeP.isNullOrEmpty()) {
                        getParentImobilisation(codeParent = it.cliImoCodeP!!)
                }
             }
        }
    }




    var allBatimentListstate: ZoneConsommationListState by mutableStateOf(ZoneConsommationListState())
        private set

    var isLoading: Boolean by mutableStateOf(false)
        private set


    fun resetAllBatimentListstate() {
        allBatimentListstate = ZoneConsommationListState()
    }
    fun onEvent(event: ListEvent) {

        when (event) {
            is ListEvent.Order -> {
                if (allBatimentListstate.listOrder::class == event.listOrder::class &&
                    allBatimentListstate.listOrder.orderType == event.listOrder.orderType
                ) {
                    return
                }
                allBatimentListstate = allBatimentListstate.copy(
                    listOrder = event.listOrder
                )
                filterZoneConsommation(allBatimentListstate)
            }
            is ListEvent.Delete -> TODO()
            is ListEvent.Restore -> TODO()


            is ListEvent.ListSearch -> {
                allBatimentListstate = allBatimentListstate.copy(
                    filter = event.listSearch
                )

                filterZoneConsommation(allBatimentListstate)
            }

            is ListEvent.FirstCustomFilter -> TODO()
            is ListEvent.SecondCustomFilter -> TODO()
            is ListEvent.ThirdCustomFilter -> TODO()
        }


    }

   // var getZoneConsommationJob: Job = Job()

    fun filterZoneConsommation(zoneConsommationListState: ZoneConsommationListState) {
        isLoading = true
        val searchedText = searchTextState.text
        val listFilter = zoneConsommationListState.filter
        val isAsc = if (zoneConsommationListState.listOrder.orderType is OrderType.Ascending) 1 else 2
        val sortBy = when (zoneConsommationListState.listOrder) {
            is ListOrder.Title -> "CLI_NomPren"
            is ListOrder.Date -> "CLI_Code"
            is ListOrder.Third -> "Clt_ImoCB"
        }

        viewModelScope.launch(ioDispatcher) {
            val resultFlow = when {
                searchedText.isEmpty() -> proCaisseLocalDb.immobilisation.getAllFiltred(
                    isAsc = isAsc,
                    sortBy = sortBy,
                    byUser = displayAffectedBatiment,
                    tyEmpImNom = tyEmpImNom
                )
                listFilter is ListSearch.FirstSearch -> proCaisseLocalDb.immobilisation.filterByName(
                    filterString = searchedText,
                    sortBy = sortBy,
                    isAsc = isAsc,
                    byUser = displayAffectedBatiment,
                    tyEmpImNom = tyEmpImNom
                )
                listFilter is ListSearch.SecondSearch -> proCaisseLocalDb.immobilisation.filterByCLICode(
                    filterString = searchedText,
                    sortBy = sortBy,
                    isAsc = isAsc,
                    byUser = displayAffectedBatiment,
                    tyEmpImNom = tyEmpImNom
                )
                listFilter is ListSearch.ThirdSearch -> proCaisseLocalDb.immobilisation.filterByCltImoCB(
                    filterString = searchedText,
                    sortBy = sortBy,
                    isAsc = isAsc,
                    byUser = displayAffectedBatiment,
                    tyEmpImNom = tyEmpImNom
                )
                else -> null
            }

            resultFlow?.collect { result ->
                setList(immo = result)
            }
        }
    }

    private fun setList(immo: List<Immobilisation>) {
        allBatimentListstate = allBatimentListstate.copy(lists = immo)
        isLoading = false
    }




    var showSearchView: Boolean by mutableStateOf(false)
        private set
    fun onShowSearchViewChange(value: Boolean) {
        showSearchView = value
    }


    var searchTextState: TextFieldValue by mutableStateOf(TextFieldValue(""))
        private set
    fun onSearchValueChange(value: TextFieldValue) {
        searchTextState = value
    }

    // var searchList = mutableStateListOf<String>()






}
