package com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.remote.api.bon_retour

import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.BonRetour
import kotlinx.coroutines.flow.Flow


interface BonRetourApi {
        suspend fun getBonRetours(baseConfig: String): Flow<DataResult<List<BonRetour>>>
        suspend fun addBatchBonRetour(baseConfig: String): Flow<DataResult<List<BonRetour>>>
}