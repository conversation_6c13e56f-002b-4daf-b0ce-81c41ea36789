package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Entity(tableName = ProCaisseConstants.TYPE_MOUVEMENT, primaryKeys = ["TyMvtCode"])
@Serializable
data class TypeMouvement(
    @SerialName("TyMvtCode")
    @ColumnInfo(name = "TyMvtCode")
    val tyMvtCode: String = "",
    @SerialName("DDm")
    @ColumnInfo(name = "DDm")
    val dDm: String? = "",
    @SerialName("Export")
    @ColumnInfo(name = "Export")
    val export: String? = "",
    @SerialName("TyEmpImUser")
    @ColumnInfo(name = "TyEmpImUser")
    val tyEmpImUser: String? = "",
    @SerialName("TyMvtDesig")
    @ColumnInfo(name = "TyMvtDesig")
    val tyMvtDesig: String = ""
)
