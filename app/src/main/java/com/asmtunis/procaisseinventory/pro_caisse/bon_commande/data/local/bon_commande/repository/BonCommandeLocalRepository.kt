package com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.local.bon_commande.repository

import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommandeWithClient
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommandeWithArticle
import kotlinx.coroutines.flow.Flow

 

interface BonCommandeLocalRepository {
    fun upsertAll(value: List<BonCommande>)
    fun upsert(value: BonCommande)

    fun updateNameClient(codeclient: String, clientName: String)
    fun updateStatus(status : String, bonCommandeM: String, devDdm: String)

    fun updateObservation(devObservation: String, devNum: String, exercie: String)
    fun setSynced(bonCommandeNum : String, bonCommandeNumM: String)

   fun notSynced(): Flow<Map<BonCommande, List<LigneBonCommande>>?>
    fun deleteAll()
    fun deleteByCodeM(codeM: String)
    fun getAllByStation(station: String): Flow<Map<BonCommande, List<LigneBonCommande>>>
    fun getAll(): Flow<Map<BonCommande, List<LigneBonCommande>>?>
    fun getByCodeCltAndStation(codeClient: String, station : String): Flow<Map<BonCommande, List<LigneBonCommande>>>


    fun getAllFiltred(isAsc: Int,  sortBy: String, station : String): Flow<Map<BonCommandeWithClient, List<LigneBonCommandeWithArticle>>>
    fun filterByCodeClient(searchString: String, sortBy: String, isAsc: Int, station : String): Flow<Map<BonCommandeWithClient, List<LigneBonCommandeWithArticle>>>
    fun filterByBonCommandeNum(searchString: String,  sortBy: String, isAsc: Int, station : String): Flow<Map<BonCommandeWithClient, List<LigneBonCommandeWithArticle>>>
    fun filterByNomClient(searchString: String, sortBy: String, isAsc: Int, station : String): Flow<Map<BonCommandeWithClient, List<LigneBonCommandeWithArticle>>>


}