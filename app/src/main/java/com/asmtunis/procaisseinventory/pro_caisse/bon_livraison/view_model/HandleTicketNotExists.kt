package com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model

import android.util.Log
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.getCurrentDateTime
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments
import com.asmtunis.procaisseinventory.pro_caisse.session_caisse.data.domaine.TicketSyncEvent
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withContext

/**
 * Extension function pour gérer le cas où le serveur retourne le code 10302 "Ticket n'existe pas"
 * Cette fonction crée un nouveau ticket localement et l'ajoute au journal de caisse
 */
suspend fun SyncBonLivraisonViewModel.handleTicketNotExists(
    ticketUpdate: TicketUpdate,
    insertFacture: Boolean
) {
    withContext(dispatcherIO) {
        try {
            Log.d("HandleTicketNotExists", "Début de la création du ticket pour: ${ticketUpdate.tIKNumTicketM}")

            // Récupérer l'ID de la session de caisse active
            val activeSessionId = getActiveSessionId()
            if (activeSessionId.isNullOrEmpty()) {
                Log.e("HandleTicketNotExists", "Aucune session de caisse active trouvée")
                return@withContext
            }

            Log.d("HandleTicketNotExists", "Session de caisse active: $activeSessionId")

            // Récupérer le ticket original depuis la base de données en utilisant les tickets non synchronisés
            val notSyncedTickets = proCaisseLocalDb.bonLivraison.notSynced().first()
            val originalTicketWithPayments = notSyncedTickets?.find { it.ticket?.tIKNumTicketM == ticketUpdate.tIKNumTicketM }

            if (originalTicketWithPayments?.ticket == null) {
                Log.e("HandleTicketNotExists", "Ticket original non trouvé: ${ticketUpdate.tIKNumTicketM}")
                return@withContext
            }

            val originalTicket = originalTicketWithPayments.ticket!!
            Log.d("HandleTicketNotExists", "Ticket original trouvé: ${originalTicket.tIKNumTicketM}")

            // Mettre à jour l'ID de session du ticket pour l'intégrer au journal de caisse
            proCaisseLocalDb.bonLivraison.updateSessionId(
                tikNumTicketM = ticketUpdate.tIKNumTicketM ?: "",
                sessionId = activeSessionId
            )

            // Mettre à jour le statut du ticket pour le journal de caisse
            proCaisseLocalDb.bonLivraison.updateTicketStatus(
                tikNumTicketM = ticketUpdate.tIKNumTicketM ?: "",
                status = "JOURNAL_CAISSE"
            )

            Log.d("HandleTicketNotExists", "Ticket mis à jour avec TIK_NumTicket: ${ticketUpdate.tIKNumTicket}, TIK_IdSCaisse: $activeSessionId")

            // Mettre à jour les lignes de ticket avec le nouveau numéro
            proCaisseLocalDb.ligneBonLivraison.updateNumTicket(
                codeM = ticketUpdate.tIKNumTicketM ?: "",
                newCode = ticketUpdate.tIKNumTicket,
                exercice = ticketUpdate.tIKExerc ?: "",
                carnet = ticketUpdate.tIKIdCarnet ?: ""
            )

            Log.d("HandleTicketNotExists", "Lignes de ticket mises à jour")

            // Mettre à jour les règlements
            proCaisseLocalDb.reglementCaisse.updateRegCodeAndState(
                regCode = ticketUpdate.tIKNumTicketM ?: "",
                regCodeM = ticketUpdate.tIKNumTicketM ?: ""
            )

            // Mettre à jour les chèques
            proCaisseLocalDb.chequeCaisse.updateRegCodeAndState(
                regCode = ticketUpdate.tIKNumTicketM ?: "",
                regCodeM = ticketUpdate.tIKNumTicketM ?: ""
            )

            Log.d("HandleTicketNotExists", "Règlements et chèques mis à jour")

            // Si autoFacture est activé, créer une facture
            if (insertFacture) {
                // Créer la facture associée au ticket
                // Cette logique peut être ajoutée si nécessaire
                Log.d("HandleTicketNotExists", "Création de facture activée")
            }

            // Émettre un événement de synchronisation pour informer les autres ViewModels
            withContext(mainDispatcher) {
                TicketSyncEvent.triggerSync()
                Log.d("HandleTicketNotExists", "Événement de synchronisation émis")
            }

            Log.d("HandleTicketNotExists", "Ticket ${ticketUpdate.tIKNumTicketM} créé avec succès dans le journal de caisse de la session $activeSessionId")

        } catch (e: Exception) {
            Log.e("HandleTicketNotExists", "Erreur lors de la création du ticket: ${e.message}", e)
        }
    }
}
