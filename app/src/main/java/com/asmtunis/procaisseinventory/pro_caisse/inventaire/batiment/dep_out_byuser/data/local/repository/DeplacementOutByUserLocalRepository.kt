package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.data.local.repository

import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.data.domaine.DeplacementOutByUser
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.data.domaine.DeplacementOutByUserWithImmobilisation
import kotlinx.coroutines.flow.Flow


interface DeplacementOutByUserLocalRepository {
    fun upsertAll(value: List<DeplacementOutByUser>)
    fun upsert(value: DeplacementOutByUser)

    fun getByNumSerie(code: String): Flow<Map<DeplacementOutByUser, List<LigneBonCommande>>?>



    fun deleteAll()

    fun getAll(station: String, devEtat: String): Flow<Map<DeplacementOutByUser, List<LigneBonCommande>>>


    fun getAllFiltred(
        onlyWaiting: String,
                      isAsc: Int,
                      sortBy: String
    ): Flow<Map<DeplacementOutByUserWithImmobilisation, List<LigneBonCommande>>>
    fun filterByNumSerie(
        onlyWaiting: String,
                         searchString: String,
                         sortBy: String,
                         isAsc: Int
    ): Flow<Map<DeplacementOutByUserWithImmobilisation, List<LigneBonCommande>>>
    fun filterByBonCommandeNum(
        onlyWaiting: String,
        searchString: String,
        sortBy: String,
        isAsc: Int
    ): Flow<Map<DeplacementOutByUserWithImmobilisation, List<LigneBonCommande>>>
    fun filterByBatiment(
        onlyWaiting: String,
                       searchString: String,
                       sortBy: String,
                       isAsc: Int
    ): Flow<Map<DeplacementOutByUserWithImmobilisation, List<LigneBonCommande>>>


}