package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.view_model

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.ListenNetwork
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.PROCAISSE_AUTO_SYNC_AUTHORISATION
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.SELECTED_BASE_CONFIG
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.core.model.GenericObject
import com.asmtunis.procaisseinventory.core.utils.DefaultDispatcher
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainImmediateDispatcher
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.BatimentCheck
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.BatimentCheckResponse
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.Immobilisation
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.cancellable
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.encodeToJsonElement
import javax.inject.Inject


@HiltViewModel
class SyncInvBatimentViewModel @Inject constructor(
    @DefaultDispatcher private val defaultDispatcher: CoroutineDispatcher,
    @IoDispatcher private val dispatcherIO: CoroutineDispatcher,
    @MainImmediateDispatcher private val mainImmediateDispatcher: CoroutineDispatcher,
    @MainDispatcher private val mainDispatcher: CoroutineDispatcher,
    private val proCaisseRemote: ProCaisseRemote,
    private val proCaisseLocalDb: ProCaisseLocalDb,
    private val listenNetwork: ListenNetwork,
    // app: Application
) : ViewModel() {

    private var autoSyncState  by mutableStateOf(false)

    private var autoSyncFlow = proCaisseLocalDb.dataStore.getBoolean(key = PROCAISSE_AUTO_SYNC_AUTHORISATION, default = true).distinctUntilChanged()

    private var  connected  by mutableStateOf(false)

    private val networkFlow = listenNetwork.isConnected.distinctUntilChanged()

    init {
            getNotSyncBatimentCodeBare()

    }




    var batimentCBNotSync: List<Immobilisation> by mutableStateOf(emptyList())
        private set



    private fun getNotSyncBatimentCodeBare() {

        viewModelScope.launch {
            val batimentCBNotSyncFlow = proCaisseLocalDb.immobilisation.getNotSync().distinctUntilChanged()


            combine(networkFlow, batimentCBNotSyncFlow, autoSyncFlow) { isConnected, batimentCBNotSyncList, autoSync ->
                connected = isConnected
                autoSyncState = autoSync
                batimentCBNotSyncList.ifEmpty { emptyList() }
            }.collect {
                if (it.isEmpty()) {
                    batimentCBNotSync = emptyList()
                    return@collect
                }
                batimentCBNotSync = it
                if(connected && autoSyncState && it.first().cliImoCB!= null)   syncAffectCodeBareBatiment(batimentCheck = BatimentCheck(
                    cLICode = it.first().cLICode,
                    cltImoCB = it.first().cliImoCB!!
                )
                )
            }
        }
    }



    var responseAffectCodeBareBatimentState: RemoteResponseState<BatimentCheckResponse>  by mutableStateOf(RemoteResponseState())
        private set

    var notSyncAffectCodeBareBatimentObj : String by mutableStateOf("")
        private set
    fun resetResponseAffectCodeBareBatimentState() {
        responseAffectCodeBareBatimentState = RemoteResponseState()
    }

    fun syncAffectCodeBareBatiment(batimentCheck: BatimentCheck, showErrorResult: Boolean = false) {

        viewModelScope.launch(dispatcherIO) {
//            affectCodeBareBatimentOffline(batimentCheck = batimentCheck, error = result.message, showErrorResult = showErrorResult)
//            return@launch
            val baseConfigObj = GenericObject(
                proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()?.let { Json.decodeFromString(it) }?: BaseConfig(),
                Json.encodeToJsonElement(batimentCheck)
            )

            notSyncAffectCodeBareBatimentObj = Json.encodeToString(baseConfigObj)
            proCaisseRemote.inventaireBatiment.affectCodeBareBatiment(notSyncAffectCodeBareBatimentObj).onEach { result ->
                when (result) {
                    is DataResult.Success -> {
                       val response = result.data!!

                        if(response.code == 10708)
                            responseAffectCodeBareBatimentState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = null,
                                message = response.message
                            )
                        else {
                            responseAffectCodeBareBatimentState = RemoteResponseState(
                                data = result.data,
                                loading = false,
                                error = null,
                                message = response.message
                            )

                          proCaisseLocalDb.immobilisation.setZoneConsomationImmoCB(
                              immoCB = batimentCheck.cltImoCB,
                              cliCode = batimentCheck.cLICode,
                              isSynced = true,
                              status = ItemStatus.SELECTED.status
                          )
                        }


                    }

                    is DataResult.Loading -> {
                        responseAffectCodeBareBatimentState = RemoteResponseState(data = null, loading = true, error = null, message = null)
                    }

                    is DataResult.Error -> {
                      //  responseAffectCodeBareBatimentState = AffectCodeBareBatimentResponse(data = null, loading = false, error = result.message)
                        verifyPatLocaly(batimentCheck = batimentCheck, error = result.message, showErrorResult = showErrorResult)
                    }
                }
            }.flowOn(dispatcherIO).launchIn(this)
        }
    }
    private suspend fun verifyPatLocaly(batimentCheck: BatimentCheck, error: String?, showErrorResult: Boolean) {
        proCaisseLocalDb.invePatrimoine.getByNumSerie(batimentCheck.cltImoCB).collect { listImmobilisation ->
                if (listImmobilisation.isNullOrEmpty()) {
                    affectCodeBareBatimentOffline(batimentCheck = batimentCheck, error = error, showErrorResult = showErrorResult)
                   return@collect // Continue to the next emission, isEmpty stays true
                } else {


                    responseAffectCodeBareBatimentState = RemoteResponseState(
                        data = null,
                        loading = false,
                        error = error,
                        message = if(showErrorResult) "Déjà affecté: '" + listImmobilisation.keys.first().dEVNum + "' (Local vérification)" else null
                    )

                    return@collect // Stop collecting (optimization, assuming first emission is representative)
                }
            }

       // return isEmpty // Return true if the list was empty, false otherwise
    }
    private fun affectCodeBareBatimentOffline(batimentCheck: BatimentCheck, error: String?, showErrorResult: Boolean) {
        viewModelScope.launch(dispatcherIO) {

            proCaisseLocalDb.immobilisation.getAllZoneConsomationByImoCB(imoCB = batimentCheck.cltImoCB).cancellable().collectLatest {
                cancel()

                //if showErrorResult show message
                if(it.isNullOrEmpty()) {
                    responseAffectCodeBareBatimentState = RemoteResponseState(
                        data = null,
                        loading = false,
                        error = error,
                        message = if(showErrorResult) "Code à Barres: ${batimentCheck.cltImoCB} valide" else null
                    )

                    proCaisseLocalDb.immobilisation.setZoneConsomationImmoCB(
                        immoCB = batimentCheck.cltImoCB,
                        cliCode = batimentCheck.cLICode,
                        isSynced = false,
                        status = ItemStatus.INSERTED_CODE_BARE.status
                    )

                    return@collectLatest
                }


                responseAffectCodeBareBatimentState = if(it.size>=2) {
                    RemoteResponseState(
                        data = null,
                        loading = false,
                        error = error,
                        message = if(showErrorResult) "Code à Barres " +batimentCheck.cltImoCB+ " affecté à "+ it.size.toString() + " zone de consomation" else null
                    )

                } else {
                    RemoteResponseState(
                        data = null,
                        loading = false,
                        error = error,
                        message = if(showErrorResult) "Code à Barres: ${batimentCheck.cltImoCB} déja affecté à "+ it.first().cLINomPren else null
                    )

                }

            }
        }
    }
}