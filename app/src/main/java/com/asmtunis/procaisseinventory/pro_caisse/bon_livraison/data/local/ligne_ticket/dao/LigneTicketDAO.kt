package com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.local.ligne_ticket.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.LIGNE_TICKET_TABLE
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.LigneTicket
import kotlinx.coroutines.flow.Flow


@Dao
interface LigneTicketDAO {
    @get:Query("SELECT * FROM $LIGNE_TICKET_TABLE")
    val all: Flow<List<LigneTicket>>

    @Query("SELECT * FROM $LIGNE_TICKET_TABLE WHERE LT_NumTicket = :lt_NumTicket and LT_IdCarnet= :LT_IdCarnet and LT_Exerc = :LT_Exerc")
    fun getByTicket(lt_NumTicket: Int, LT_IdCarnet: String, LT_Exerc: String):  Flow<List<LigneTicket>>


    @Query("SELECT sum(LT_MtTTC) FROM $LIGNE_TICKET_TABLE WHERE LT_NumTicket = :lt_NumTicket and LT_Exerc=:exercice")
    fun getSumPriceByNumTicket(lt_NumTicket: Int, exercice: String):  Flow<Double>

    @get:Query("SELECT * FROM $LIGNE_TICKET_TABLE LIMIT 1")
    val one:  Flow<LigneTicket>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: LigneTicket)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<LigneTicket>)

    @Query("Update  $LIGNE_TICKET_TABLE set  LT_NumTicket=:newCode, isSync = 1, Status= 'SELECTED' where LT_NumTicket_M=:codeM and LT_Exerc=:exercice and LT_IdCarnet=:carnet")
    fun updateNumTicket(codeM: String, newCode: Int, exercice: String, carnet: String)

    // @Query("DELETE FROM LigneTicket where Status='SELECTED'")
    // void deleteAll();
    @Query("DELETE FROM $LIGNE_TICKET_TABLE")
    fun deleteAll()

    @Query("DELETE FROM $LIGNE_TICKET_TABLE where LT_NumTicket=:numTicket and LT_Exerc = :ltExercice")
    fun deleteByNumTicket(numTicket: String, ltExercice: String)

    @Query("DELETE FROM $LIGNE_TICKET_TABLE where LT_NumTicket_M=:numTicket and LT_Exerc = :ltExercice")
    fun deleteByTicketM(numTicket: String, ltExercice: String)
}
