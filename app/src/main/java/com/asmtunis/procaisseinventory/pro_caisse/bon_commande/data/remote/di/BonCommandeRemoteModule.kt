package com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.remote.di

import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.remote.api.bon_commande.BonCommandeApi
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.remote.api.bon_commande.BonCommandeApiImpl
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.remote.api.ligne_bon_commande.LigneBonCommandeApi
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.remote.api.ligne_bon_commande.LigneBonCommandeApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object BonCommandeRemoteModule {

    @Provides
    @Singleton
    fun provideBonCommandeApi(client: HttpClient): BonCommandeApi = BonCommandeApiImpl(client)

    @Provides
    @Singleton
    fun provideLigneBonCommandeApi(client: HttpClient): LigneBonCommandeApi = LigneBonCommandeApiImpl(client)


}