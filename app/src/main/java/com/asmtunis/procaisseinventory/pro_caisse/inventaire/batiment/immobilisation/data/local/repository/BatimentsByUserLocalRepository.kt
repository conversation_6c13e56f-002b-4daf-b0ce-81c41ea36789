package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.local.repository

import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.BatimentByUser
import kotlinx.coroutines.flow.Flow



interface BatimentsByUserLocalRepository {
    fun getAll(): Flow<List<BatimentByUser>?>

    fun insert(item: BatimentByUser)

    fun insertAll(items: List<BatimentByUser>)

    fun deleteAll()


    fun getAllFiltred(isAsc: Int, sortBy: String): Flow<List<BatimentByUser>>
    fun filterByCltImoCB(filterString: String, sortBy: String, isAsc: Int): Flow<List<BatimentByUser>>
    fun filterByCLICode(filterString: String, sortBy: String, isAsc: Int): Flow<List<BatimentByUser>>
    fun filterByName(filterString: String, sortBy: String, isAsc: Int): Flow<List<BatimentByUser>>

}