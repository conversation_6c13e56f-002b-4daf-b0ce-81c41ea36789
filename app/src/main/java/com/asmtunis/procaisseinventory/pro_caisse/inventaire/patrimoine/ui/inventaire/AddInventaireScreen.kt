package com.asmtunis.procaisseinventory.pro_caisse.inventaire.patrimoine.ui.inventaire

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Save
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.data.marque.domaine.Marque
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.Constants
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.SetNumSerieView
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.domaine.ControleInventaire
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.updateInvPatQty
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.openBareCodeScanner
import com.asmtunis.procaisseinventory.shared_ui_components.tables.three_column.ThreeColumnTableWithImage
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.ProCaisseViewModels
import com.dokar.sonner.rememberToasterState

@Composable
fun AddInventaireScreen(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    navigateUp: () -> Unit,
    clientId: String,
    navDrawerViewModel: NavigationDrawerViewModel,
    mainViewModel: MainViewModel,
    barCodeViewModel: BarCodeViewModel,
    proCaisseViewModels: ProCaisseViewModels,
    dataViewModel: DataViewModel,
    settingViewModel: SettingViewModel,
    networkViewModel: NetworkViewModel
) {
    val selectPatrimoineVM = proCaisseViewModels.selectPatrimoineVM
    val invPatViewModel = proCaisseViewModels.invPatViewModel

    val context = LocalContext.current

    val articleMapByBarCode = mainViewModel.articleMapByBarCode
    val imageList = mainViewModel.imageList
    val selectedPatList = selectPatrimoineVM.selectedPatrimoineList
    val selectedPatrimoine = selectPatrimoineVM.selectedPatrimoine
    val marqueList = mainViewModel.marqueList
    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig
    val utilisateur = mainViewModel.utilisateur

    val clientList = mainViewModel.clientList
    val clientByCode = clientList.firstOrNull { it.cLICode == clientId }?: Client()//mainViewModel.clientByCode

    val barCodeInfo = barCodeViewModel.barCodeInfo
    val haveCamera = dataViewModel.getHaveCameraDevice()
    val codeM = mainViewModel.codeM
    val patrimoineVerificationState = selectPatrimoineVM.patrimoineVerificationState
    val invPatByNumSerie = selectPatrimoineVM.invPatByNumSerie
    val typeInvetaireState = invPatViewModel.typeInvetaireState

    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)
    Scaffold(
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                onNavigationClick = { popBackStack() },
                navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                title = codeM,
            )
        },
        floatingActionButton = {
            Column {
                FloatingActionButton(
                    onClick = {
                        selectPatrimoineVM.resetPatrimoineVerificationState()
                        selectPatrimoineVM.onShowSetNumeSerieChange(true)
                    }) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = stringResource(id = R.string.cd_achat_button)
                    )
                }


                Spacer(modifier = Modifier.height(12.dp))
                FloatingActionButton(
                    onClick = {
                        invPatViewModel.saveInvPat(
                            articleMapByBarCode = articleMapByBarCode,
                            codeM = codeM,
                            listSelectedPatrimoine = selectedPatList,
                            exercice = mainViewModel.exerciceList.first().exerciceCode,
                            client = clientByCode,
                            utilisateur = utilisateur,
                            typeInv = typeInvetaireState,
                            devEtat = Constants.PATRIMOINE,
                            onComplete = { navigateUp() }
                        )


                    }) {
                    Icon(
                        imageVector = Icons.Default.Save,
                        contentDescription = stringResource(id = R.string.cd_achat_button)
                    )
                }
            }

        }
    ) { padding ->
        if (selectPatrimoineVM.showSetNumeSerie) {
            SetNumSerieView(
                haveCamera = haveCamera,
                articleMapByBarCode = articleMapByBarCode,
                marqueList = marqueList,
                selectedPatrimoine = selectedPatrimoine,
                selectedPatrimoineList = selectedPatList,
                onNumSerieChange = {
                    selectPatrimoineVM.setSelectedPat(selectedPatrimoine.copy(numSerie = it))
                },
                onDismiss = {

                    selectPatrimoineVM.onShowSetNumeSerieChange(false)
                    //  selectPatrimoineVM.resetPatrimoineVerificationState()
                },
                onConfirm = {
                    val controlInvPat = ControleInventaire(
                        LG_DEV_NumSerie = selectedPatrimoine.numSerie,
                        DEV_CodeClient = clientByCode.cLICode,
                        DEV_info3 = typeInvetaireState
                    )
                    selectPatrimoineVM.patrimoineVerification(
                        baseConfig = mainViewModel.selectedBaseconfig,
                        controlPatrimoine = controlInvPat
                    )

                },
                onAddInvPat = {
                    selectPatrimoineVM.onShowSetNumeSerieChange(false)
                    val codeArt =
                        if (invPatByNumSerie.isNotEmpty()) invPatByNumSerie.entries.first().value.first().lGDEVCodeArt else "N/A"
                    updateInvPatQty(
                        imageList = imageList,
                        articleCode = codeArt,
                        numeSerie = selectedPatrimoine.numSerie,
                        patrimoineVerificationState = patrimoineVerificationState,
                        selectedPatrimoineList = selectedPatList,
                        addItemToSelectedPatrimoineList = { selectPatrimoineVM.addItemToSelectedPatrimoineList(it) },
                        marque = marqueList.firstOrNull { it.mARCode == selectedPatrimoine.marqueCode }?: Marque(),
                        note = selectedPatrimoine.note
                    )

                    selectPatrimoineVM.resetPatrimoineVerificationState()

                },
                onBareCodeScan = {
                    openBareCodeScanner(
                        navigate = { navigate(it) },
                        onBarCodeInfo = { barCodeViewModel.onBarCodeInfo(barCode = it) }
                    )
                },
                barCodeInfo = barCodeInfo,
                patrimoineVerificationState = patrimoineVerificationState,
                showDropDownMenuComposable = true,
                onNoteChange = {
                    selectPatrimoineVM.setSelectedPat(selectedPatrimoine.copy(note = it))
                }

            )
        }

        Column(
            verticalArrangement = Arrangement.Top,
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
        ) {
            Text(text = clientByCode.cLINomPren)
            ThreeColumnTableWithImage(
                articleMapByBarCode = articleMapByBarCode,
                marqueList = marqueList,
                haveCamera = haveCamera,
                canModify = true,
                selectedPatrimoineList = selectedPatList,
                onPress = {
                    //TODO Maybe show more detail : TVA / DISCOUNT / . . .
                }
            )
        }
    }
}
