package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.ui

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.Scaffold
import androidx.compose.material3.windowsizeclass.WindowWidthSizeClass
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.platform.LocalContext
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.asmtunis.procaisseinventory.data.marque.domaine.Marque
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.ui.adaptive_view.DepOutByUserColumnView
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.ui.adaptive_view.DepOutByUserRowView
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.ProCaisseViewModels

@Composable
fun DeplacementOutByUserDetailScreen(
    navigate: (route: String) -> Unit,
    popBackStack: () -> Unit,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    mainViewModel: MainViewModel,
    proCaisseViewModels: ProCaisseViewModels,
    settingViewModel: SettingViewModel
) {
    val selectPatrimoineVM = proCaisseViewModels.selectPatrimoineVM
    val deplacementOutByUserViewModel = proCaisseViewModels.deplacementOutByUserViewModel
    val articleMapByBarCode = mainViewModel.articleMapByBarCode
    val imageList = mainViewModel.imageList
    val context = LocalContext.current

    val deplacementOutByUser = deplacementOutByUserViewModel.selectedDeplacementOutByUser
    val ligneBonCommande = deplacementOutByUserViewModel.selectedListLg
    val selectedPatrimoineList = selectPatrimoineVM.selectedPatrimoineList
    val marqueList = mainViewModel.marqueList
    val selectedBaseconfig: BaseConfig  = dataViewModel.selectedBaseConfig

    val haveCamera = dataViewModel.getHaveCameraDevice()

    val uiWindowState = settingViewModel.uiWindowState
    val windowSize = uiWindowState.windowSize!!
    LaunchedEffect(key1 = Unit) {
        selectPatrimoineVM.resetSelectedPatrimoineArticles()
        for (i in ligneBonCommande.indices) {
            val article = articleMapByBarCode[ligneBonCommande[i].lGDEVCodeArt]?: Article(aRTCodeBar = ligneBonCommande[i].lGDEVCodeArt)
            val imgList = imageList.filter { it.vcNumSerie ==  ligneBonCommande[i].lGDevNumSerie }
            selectPatrimoineVM.setConsultationSelectedPatrimoineList(
                article = article,
                numSerie = ligneBonCommande[i].lGDevNumSerie ?: "N/A",
                quantity = StringUtils.stringToDouble(ligneBonCommande[i].lGDEVQte),
                imageList = imgList,
                note = ligneBonCommande[i].lgDEVNote ?: "",
                marque = marqueList.firstOrNull { it.mARCode == ligneBonCommande[i].lGDEVCMarq } ?: Marque(mARDesignation = ligneBonCommande[i].lGDEVCMarq?:"N/A")

            )
        }
    }

    Scaffold(
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                onNavigationClick = {
                    popBackStack()
                },
                navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                title = context.getString(R.string.invpat_number_field, deplacementOutByUser.deplacementOutByUser!!.dEVNum),
            )
        },
    ) { padding ->

        when (windowSize.widthSizeClass) {
            WindowWidthSizeClass.Compact -> {
                DepOutByUserColumnView(
                    articleMapByBarCode = articleMapByBarCode,
                    haveCamera = haveCamera,
                    marqueList = marqueList,
                    padding = padding,
                    deplacementOutByUser = deplacementOutByUser,
                selectedPatrimoineList = selectedPatrimoineList
                )
            }

            WindowWidthSizeClass.Medium,
            WindowWidthSizeClass.Expanded -> {
                DepOutByUserRowView(
                    articleMapByBarCode = articleMapByBarCode,
                    haveCamera = haveCamera,
                    marqueList = marqueList,
                    padding = padding,
                    deplacementOutByUser = deplacementOutByUser,
                    selectedPatrimoineList = selectedPatrimoineList
                )
            }
            else -> {
                DepOutByUserColumnView(
                    articleMapByBarCode = articleMapByBarCode,
                    marqueList = marqueList,
                    haveCamera = haveCamera,
                    padding = padding,
                    deplacementOutByUser = deplacementOutByUser,
                    selectedPatrimoineList = selectedPatrimoineList
                )
            }
        }
    }
}
