package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.zone_consomation


import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.Immobilisation
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.ListOrder
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.OrderType
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch

data class ZoneConsommationListState(
    val lists: List<Immobilisation> = emptyList(),
    val listOrder: ListOrder = ListOrder.Title(OrderType.Descending),
    val filter: ListSearch = ListSearch.FirstSearch()

    )
