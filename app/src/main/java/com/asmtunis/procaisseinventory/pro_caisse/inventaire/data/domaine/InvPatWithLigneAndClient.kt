package com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.domaine

import androidx.room.Embedded
import androidx.room.Relation
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client

data class InvPatWithLigneAndClient (
    @Embedded val invPat : BonCommande,
    @Relation(
        parentColumn = "DEV_Num",
        entityColumn = "LG_DEV_NumBon"
    )
    val ligneInvPat : List<LigneBonCommande>,

    @Relation(
        parentColumn = "DEV_Client",
        entityColumn = "CLI_Code"
    )
    val client : Client,

    )