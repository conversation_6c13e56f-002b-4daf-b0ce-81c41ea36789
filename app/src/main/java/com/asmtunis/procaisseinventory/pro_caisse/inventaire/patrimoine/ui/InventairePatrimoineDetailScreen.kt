package com.asmtunis.procaisseinventory.pro_caisse.inventaire.patrimoine.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.tables.three_column.ThreeColumnTableWithImage
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.ProCaisseViewModels


@Composable
fun InventairePatrimoineDetailScreen(
    navigate: (route: String) -> Unit,
    popBackStack: () -> Unit,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    mainViewModel: MainViewModel,
    proCaisseViewModels: ProCaisseViewModels
) {
    val invPatrimoineViewModel = proCaisseViewModels.invPatViewModel
    val selectPatrimoineVM = proCaisseViewModels.selectPatrimoineVM
    val context = LocalContext.current

    val bonCommande = invPatrimoineViewModel.selectedInvPatrimoine
    val listLigneBonCommande = invPatrimoineViewModel.selectedListLgInvPatrimoine

    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig

    val articleMapByBarCode = mainViewModel.articleMapByBarCode
    val imageList = mainViewModel.imageList
    val marqueList = mainViewModel.marqueList

    LaunchedEffect(key1 = Unit) {
        selectPatrimoineVM.resetSelectedPatrimoineArticles()
        for (i in listLigneBonCommande.indices) {
            val article = articleMapByBarCode[listLigneBonCommande[i].lGDEVCodeArt]?: Article(aRTCodeBar = listLigneBonCommande[i].lGDEVCodeArt)
            val imgList = imageList.filter { it.vcNumSerie ==  listLigneBonCommande[i].lGDevNumSerie }

            selectPatrimoineVM.setConsultationSelectedPatrimoineList(
                article = article,
                numSerie = listLigneBonCommande[i].lGDevNumSerie ?: "N/A",
                quantity = StringUtils.stringToDouble(listLigneBonCommande[i].lGDEVQte),
                imageList = imgList,
                note = listLigneBonCommande[i].lgDEVNote ?: ""
            )


        }
    }


    Scaffold(
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                onNavigationClick = { popBackStack() },
                navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                title = context.getString(R.string.invpat_number_field, bonCommande.dEVNum),
            )
        }
    ) { padding ->
        Column(
            verticalArrangement = Arrangement.Top,
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
        ) {
            Text(
                text = invPatrimoineViewModel.selectedInvPatrimoine.dEVClientName ?: "N/A",
                //  color = MaterialTheme.colorScheme.error,
                fontSize = MaterialTheme.typography.bodyLarge.fontSize,
                // modifier = Modifier.align(Alignment.Start)
            )
            Spacer(modifier = Modifier.height(12.dp))

            Text(
                text = invPatrimoineViewModel.selectedInvPatrimoine.dEVDDm?.replace(".000", "")
                    ?: "N/A",
                //  color = MaterialTheme.colorScheme.error,
                fontSize = MaterialTheme.typography.bodyLarge.fontSize,
                // modifier = Modifier.align(Alignment.Start)
            )


            Spacer(modifier = Modifier.height(20.dp))


            ThreeColumnTableWithImage(
                articleMapByBarCode = articleMapByBarCode,
                marqueList = marqueList,
                haveCamera = dataViewModel.getHaveCameraDevice(),
                selectedPatrimoineList = selectPatrimoineVM.selectedPatrimoineList,
                onPress = {
                    //TODO Maybe show more detail : TVA / DISCOUNT / . . .
                }
            )
        }
    }


}