package com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.remote.di

import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.remote.api.ligne_ticket.LigneTicketApi
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.remote.api.ligne_ticket.LigneTicketApiImpl
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.remote.api.ticket.TicketApi
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.remote.api.ticket.TicketApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object TicketRemoteModule {


    @Provides
    @Singleton
    fun provideTicketApi(client: HttpClient): TicketApi = TicketApiImpl(client)

    @Provides
    @Singleton
    fun provideLigneTicketApi(client: HttpClient): LigneTicketApi = LigneTicketApiImpl(client)

}