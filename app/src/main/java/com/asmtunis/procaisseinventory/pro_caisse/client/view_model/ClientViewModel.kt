package com.asmtunis.procaisseinventory.pro_caisse.client.view_model

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.text.input.TextFieldValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.Globals.ACTIVE
import com.asmtunis.procaisseinventory.core.Globals.BCC_CLIENT
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.asmtunis.procaisseinventory.data.exercice.domaine.Exercice
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.LigneTicket
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.Ticket
import com.asmtunis.procaisseinventory.pro_caisse.client.ClientListState
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.client.text_validation.ValidationAddClientEvent
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.LigneVisitesDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.VisitesDn
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.ListEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.ListOrder
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.OrderType
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import com.simapps.ui_kit.utils.getCurrentDateTime
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ClientViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    private val proCaisseRemote: ProCaisseRemote,
    private val proCaisseLocalDb: ProCaisseLocalDb
) : ViewModel() {

    var selectedClient by  mutableStateOf(Client())
        private set
    fun onSelectedClientChange(value: Client) {
        selectedClient = value
    }


    var progress by  mutableFloatStateOf(0f)
        private set
    fun onProgressChange(value: Float) {
        progress = value
    }


    var toolbarOffsetHeightPx by mutableFloatStateOf(0f)
        private set
    fun onToolbarOffsetHeightPxChange(value: Float) {
        toolbarOffsetHeightPx = value
    }

    var showMapView  by mutableStateOf(false)
        private set
    fun onShowMapViewChange(value: Boolean) {
        showMapView = value
    }

    var typeClientExpanded  by mutableStateOf(false)
        private set
    fun onTypeClientExpandedChange(value: Boolean) {
        typeClientExpanded = value
    }


    var filterBySold  by mutableIntStateOf(2)
        private set
    fun onfilterBySoldChange(value: Int) {
        filterBySold = value
    }

    var showCustomFilter  by mutableStateOf(false)
        private set
    fun onShowCustomFilterChange(value: Boolean) {
        showCustomFilter = value
    }











    fun saveClient(client: Client) {
        viewModelScope.launch(dispatcher) {
            proCaisseLocalDb.clients.upsert(client)
        }
    }

    var visitesList: Map<VisitesDn, List<LigneVisitesDn>> by mutableStateOf(emptyMap())
        private set
    fun getVisitesListByClient(codeClient: String) {
        viewModelScope.launch {
            proCaisseLocalDb.visitesDn.getVisiteListByClient(codeClient).collect {
                visitesList = it
            }
        }
    }


    fun deleteClient(client: Client) {
        viewModelScope.launch(dispatcher) {
            proCaisseLocalDb.clients.delete(client)
        }
    }


    var bcList: Map<BonCommande, List<LigneBonCommande>> by mutableStateOf(emptyMap())
        private set
    var invPatList: Map<BonCommande, List<LigneBonCommande>> by mutableStateOf(emptyMap())
        private set
    fun getBCListByClientAndStation(codeClient: String, station : String) {
        viewModelScope.launch {
            proCaisseLocalDb.bonCommande.getByCodeCltAndStation(codeClient = codeClient, station = station).collect { bonCommandeListMap ->


                bcList = bonCommandeListMap.filter { it.key.dEVEtat == BCC_CLIENT }
                invPatList = bonCommandeListMap.filter { it.key.dEVEtat != BCC_CLIENT }
            }
        }
    }



    var blList: Map<Ticket, List<LigneTicket>> by mutableStateOf(emptyMap())
        private set
    fun getBLListByClientAndSession(codeClient: String, sCIdSCaisse: String) {
        viewModelScope.launch {
            proCaisseLocalDb.bonLivraison.getByClient(codeClient = codeClient, sCIdSCaisse = sCIdSCaisse).collect {
                blList = it
            }
        }
    }




    //   var isFiltering: Boolean by mutableStateOf(false)
    //   private set

    private var recentlyDeletedClient: Client? = null

    var clientsListstate: ClientListState by mutableStateOf(ClientListState())
        private set
    fun onEvent(event: ListEvent) {
        when (event) {
            is ListEvent.Order -> {
                if (clientsListstate.listOrder::class == event.listOrder::class &&
                    clientsListstate.listOrder.orderType == event.listOrder.orderType
                ) {
                    return
                }
                clientsListstate = clientsListstate.copy(
                    listOrder = event.listOrder
                )
                filterClients(clientsListstate)
            }
            is ListEvent.Delete -> {
                viewModelScope.launch(dispatcher) {
                    proCaisseLocalDb.clients.delete(event.mainTable as Client)
                    //  local.clientsUseCases.deleteClient(event.note)
                    recentlyDeletedClient = event.mainTable
                }
            }
            is ListEvent.Restore -> {
                viewModelScope.launch(dispatcher) {
                    proCaisseLocalDb.clients.upsert((recentlyDeletedClient ?: return@launch))
                    recentlyDeletedClient = null
                }
            }


            is ListEvent.ListSearch -> {
                clientsListstate = clientsListstate.copy(
                    filter = event.listSearch
                )

                filterClients(clientsListstate)
            }

            is ListEvent.FirstCustomFilter -> {
                clientsListstate = clientsListstate.copy(
                    filterByType = event.firstFilter
                )

                filterClients(clientsListstate)
            }
            is ListEvent.SecondCustomFilter -> {
                clientsListstate = clientsListstate.copy(
                    filterByClientEtat = event.secondFiter
                )

                filterClients(clientsListstate)
            }
            is ListEvent.ThirdCustomFilter -> TODO()
        }
    }

    var getClientsJob: Job = Job()

    fun filterClients(clientListState: ClientListState) {
        val searchedText = searchTextState.text
        val listFilter = clientListState.filter
        val filterByType = clientListState.filterByType
        val filterByClientEtat = clientListState.filterByClientEtat

         getClientsJob.cancel()

        if (searchedText.isEmpty()) {
            getClientsJob = when (clientListState.listOrder.orderType) {
                is OrderType.Ascending -> {
                    when (clientListState.listOrder) {
                        is ListOrder.Title -> viewModelScope.launch {
                            proCaisseLocalDb.clients.getAllFiltred(isAsc = 1, sortBy = "CLI_NomPren", sold = filterBySold, filterType =filterByType, filterByClientEtat = filterByClientEtat).collect {
                                setClientList(clientList = it)
                            }
                        }

                        is ListOrder.Date -> viewModelScope.launch {
                            proCaisseLocalDb.clients.getAllFiltred(isAsc = 1, sortBy = "CLI_DDm", sold = filterBySold, filterType =filterByType, filterByClientEtat = filterByClientEtat).collect {
                                setClientList(clientList = it)
                            }
                        }

                        is ListOrder.Third -> viewModelScope.launch {
                            proCaisseLocalDb.clients.getAllFiltred(isAsc = 1, sortBy = "Solde", sold = filterBySold, filterType =filterByType, filterByClientEtat = filterByClientEtat).collect {
                                setClientList(clientList = it)
                            }
                        }
                    }
                }

                is OrderType.Descending -> {
                    when (clientListState.listOrder) {
                        is ListOrder.Title -> viewModelScope.launch {
                            proCaisseLocalDb.clients.getAllFiltred(isAsc = 2, sortBy = "CLI_NomPren", sold = filterBySold, filterType =filterByType, filterByClientEtat = filterByClientEtat).collect {
                                setClientList(clientList = it)
                            }
                        }

                        is ListOrder.Date -> viewModelScope.launch {
                            proCaisseLocalDb.clients.getAllFiltred(isAsc = 2, sortBy = "CLI_DDm", sold = filterBySold, filterType =filterByType, filterByClientEtat = filterByClientEtat).collect {
                                setClientList(clientList = it)
                            }
                        }

                        is ListOrder.Third -> viewModelScope.launch {
                            proCaisseLocalDb.clients.getAllFiltred(isAsc = 2, sortBy = "Solde", sold = filterBySold, filterType =filterByType, filterByClientEtat = filterByClientEtat).collect {
                                setClientList(clientList = it)
                            }
                        }
                    }
                }
            }
        }
        else {
            if (searchedText.isNotEmpty()) {
                if (listFilter is ListSearch.FirstSearch) {
                    getClientsJob = when (clientListState.listOrder.orderType) {
                        is OrderType.Ascending -> {
                            when (clientListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.clients.filterByName(searchedText, sortBy = "CLI_NomPren", isAsc = 1, sold = filterBySold, filterType =filterByType, filterByClientEtat = filterByClientEtat).collect {
                                        setClientList(clientList = it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.clients.filterByName(searchedText, sortBy = "CLI_DDm", isAsc = 1, sold = filterBySold, filterType =filterByType, filterByClientEtat = filterByClientEtat).collect {
                                        setClientList(clientList = it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.clients.filterByName(searchedText, sortBy = "Solde", isAsc = 1, sold = filterBySold, filterType =filterByType, filterByClientEtat = filterByClientEtat).collect {
                                        setClientList(clientList = it)
                                    }
                                }
                            }
                        }

                        is OrderType.Descending -> {
                            when (clientListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.clients.filterByName(searchedText, sortBy = "CLI_NomPren", isAsc = 2, sold = filterBySold, filterType =filterByType, filterByClientEtat = filterByClientEtat).collect {
                                        setClientList(clientList = it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.clients.filterByName(searchedText, sortBy = "CLI_DDm", isAsc = 2, sold = filterBySold, filterType =filterByType, filterByClientEtat = filterByClientEtat).collect {
                                        setClientList(clientList = it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.clients.filterByName(searchedText, sortBy = "Solde", isAsc = 2, sold = filterBySold, filterType =filterByType, filterByClientEtat = filterByClientEtat).collect {
                                        setClientList(clientList = it)
                                    }
                                }
                            }
                        }
                    }
                }

                if (listFilter is ListSearch.SecondSearch) {
                    getClientsJob = when (clientListState.listOrder.orderType) {
                        is OrderType.Ascending -> {
                            when (clientListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.clients.filterByCLICode(searchedText, sortBy = "CLI_NomPren", isAsc = 1, sold = filterBySold, filterType =filterByType, filterByClientEtat = filterByClientEtat).collect {
                                        setClientList(clientList = it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.clients.filterByCLICode(searchedText, sortBy = "CLI_DDm", isAsc = 1, sold = filterBySold, filterType = filterByType, filterByClientEtat = filterByClientEtat).collect {
                                        setClientList(clientList = it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.clients.filterByCLICode(searchedText, sortBy = "Solde", isAsc = 1, sold = filterBySold, filterType =filterByType, filterByClientEtat = filterByClientEtat).collect {
                                        setClientList(clientList = it)
                                    }
                                }
                            }
                        }

                        is OrderType.Descending -> {
                            when (clientListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.clients.filterByCLICode(searchedText, sortBy = "CLI_NomPren", isAsc = 2, sold = filterBySold, filterType =filterByType, filterByClientEtat = filterByClientEtat).collect {
                                        setClientList(clientList = it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.clients.filterByCLICode(searchedText, sortBy = "CLI_DDm", isAsc = 2, sold = filterBySold, filterType =filterByType, filterByClientEtat = filterByClientEtat).collect {
                                        setClientList(clientList = it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.clients.filterByCLICode(searchedText, sortBy = "Solde", isAsc = 2, sold = filterBySold, filterType =filterByType, filterByClientEtat = filterByClientEtat).collect {
                                        setClientList(clientList = it)
                                    }
                                }
                            }
                        }
                    }
                }

                if (listFilter is ListSearch.ThirdSearch) {
                    getClientsJob =      when (clientListState.listOrder.orderType) {
                        is OrderType.Ascending -> {
                            when (clientListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.clients.filterByCLICode(searchedText, sortBy = "CLI_NomPren", isAsc = 1, sold = filterBySold, filterType =filterByType, filterByClientEtat = filterByClientEtat).collect {
                                        setClientList(clientList = it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.clients.filterByCLICode(searchedText, sortBy = "CLI_DDm", isAsc = 1, sold = filterBySold, filterType =filterByType, filterByClientEtat = filterByClientEtat).collect {
                                        setClientList(clientList = it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.clients.filterByCLICode(searchedText, sortBy = "Solde", isAsc = 1, sold = filterBySold, filterType =filterByType, filterByClientEtat = filterByClientEtat).collect {
                                        setClientList(clientList = it)
                                    }
                                }
                            }
                        }

                        is OrderType.Descending -> {
                            when (clientListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.clients.filterByCLICode(searchedText, sortBy = "CLI_NomPren", isAsc = 2, sold = filterBySold, filterType =filterByType, filterByClientEtat = filterByClientEtat).collect {
                                        setClientList(clientList = it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.clients.filterByCLICode(searchedText, sortBy = "CLI_DDm", isAsc = 2, sold = filterBySold, filterType =filterByType, filterByClientEtat = filterByClientEtat).collect {
                                        setClientList(clientList = it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.clients.filterByCLICode(searchedText, sortBy = "Solde", isAsc = 2, sold = filterBySold, filterType =filterByType, filterByClientEtat = filterByClientEtat).collect {
                                        setClientList(clientList = it)
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }


    private fun setClientList(clientList: List<Client>){
        clientsListstate = clientsListstate.copy(lists =  emptyList())

        clientsListstate = clientsListstate.copy(lists = clientList)
      //  getClientsJob.cancel()
    }

    var showSearchView: Boolean by mutableStateOf(false)
        private set
    fun onShowSearchViewChange(value: Boolean) {
        showSearchView = value
    }

    var filteredClients: List<Client> by mutableStateOf(emptyList())
        private set

    // val textState = remember { mutableStateOf(TextFieldValue("")) }
    var searchTextState: TextFieldValue by mutableStateOf(TextFieldValue(""))
        private set
    fun onSearchValueChange(value: TextFieldValue) {
        searchTextState = value
    }

    // var searchList = mutableStateListOf<String>()

    var searchValue by mutableStateOf("")
        private set

    fun onsearchValue(product: String) {
        searchValue = product
    }

    var searchViewVisibilityStates by mutableStateOf(false)
    fun onsearchViewVisibilityStatesChanged(bottomNavigationBar: Boolean) {
        searchViewVisibilityStates = bottomNavigationBar
    }




    fun handleAddClientEvents(
        codeM: String,
        utilisateur: Utilisateur,
        exerciceList: List<Exercice>,
        popBackStack: () -> Unit,
        validationAddClientEvents: ValidationAddClientEvent,
        clientByCode: Client,
    ) {
                when (validationAddClientEvents) {
                    is ValidationAddClientEvent.AddClient -> {
                        // TODO
                        /**
                         * TO DO
                         * if (App.database.prefixeDAO().getOneById("client") == null) {
                        Toasty.warning(context, getString(R.string.there_is_no_client_prefix_try_update_data_base)).show();
                        return;
                        }
                         */
                        // TODO
                        /**
                         * get coordinate * get adresse
                         */

                        val stationUtilisateur = utilisateur.Station

                        val userID = utilisateur.codeUt

                        val input = validationAddClientEvents.addClient

//                        showToast(
//                            context = context,
//                            toaster = toaster,
//                            message = context.resources.getString(R.string.add_client_success) + " \n"+ input.nom,
//                            type =  ToastType.Success,
//                        )


                        var currentClt = clientByCode

                        currentClt =
                            currentClt.copy(   //id = if(autreViewModel.selectedAutre.id!=0L) autreViewModel.selectedAutre.id else 0,
                                id = if (currentClt.id != 0L) currentClt.id else 0,
                                cLICode = currentClt.cLICode.ifEmpty { codeM },
                                cLICodeM = codeM,
                                cLIAdresse = input.addresse,
                                cltGouvernorat = input.gouvernorat,
                                cltVille = input.delegation,
                                cLITel1 = input.phone1,
                                cLITel2 = input.phone2,
                                cLIMail = input.email,
                                cLITimbre = if (input.haveTimbre) "1" else "0",
                                cLIIsCredit = if (input.haveCredit) "1" else "0",
                                cLINomPren = input.nom,
                                cLIMatFisc = input.matriculeFiscal,
                                cLIDateCre = getCurrentDateTime(),
                                cLtNomMagasin = input.nomSociete,
                                cLIFodec = currentClt.cLIFodec.ifEmpty { "0" },
                                cLIType = input.typeClient,
                                cltInfo1 = input.typeClient,
                                cLIExeno = currentClt.cLIExeno.ifEmpty { "False" },
                                cLIDDmM = getCurrentDateTime(),
                                cLtProfession = input.proffession,
                                cLIUser = userID,
                                cLIStation = stationUtilisateur,
                                exercice = exerciceList.first().exerciceCode,
                                cLIExoValable = getCurrentDateTime(),
                                cltLatitude = StringUtils.stringToDouble(input.latitude),
                                cltLongitude = StringUtils.stringToDouble(input.longitude),
                                solde = currentClt.solde.ifEmpty { "0" },
                                cLISolde = currentClt.cLISolde?:"0.0",
                                cLIDC = currentClt.cLIDC.ifEmpty { "0" },
                                cLIExport = currentClt.cLIExport.ifEmpty { "0" },
                                cLIForfetaire = currentClt.cLIForfetaire.ifEmpty { "0" },
                                cLIIndustrielle = currentClt.cLIIndustrielle.ifEmpty { "0" },
                                cLITauxRemGlob = currentClt.cLITauxRemGlob?: "0",
                                cLIEtat = ACTIVE
                            )

                        currentClt.isSync = false
                        currentClt.status = ItemStatus.INSERTED.status

                        saveClient(currentClt)

                        popBackStack()
                    }
                }
    }
}
