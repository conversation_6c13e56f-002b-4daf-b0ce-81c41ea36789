package com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import com.asmtunis.procaisseinventory.core.model.BaseModel
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient


//@Entity(tableName = ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE, primaryKeys = ["LG_DEV_NumBon", "LG_DEV_Exerc", "LG_DEV_CodeArt", "LG_DEV_NumOrdre"])

@Entity(tableName = ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE, primaryKeys = ["LG_DEV_NumBon", "LG_DEV_Exerc", "LG_DEV_CodeArt", "LG_DEV_NumOrdre"], indices = [Index(value = ["LG_DEV_NumBon"], unique = false)])
@Serializable
data class LigneBonCommande (
    @ColumnInfo(name = "LG_DEV_NumBon")
    @SerialName("LG_DEV_NumBon")
    var lGDEVNumBon: String = "",



    @ColumnInfo(name = "LG_DEV_Code_M")
    @SerialName("LG_DEV_Code_M")
    var lGDEVCodeM: String? = "",

    @SerialName("LG_DEV_Exerc")
    @ColumnInfo(name = "LG_DEV_Exerc")
    var lGDEVExerc: String = "",

    @ColumnInfo(name = "LG_DEV_CodeArt")
    @SerialName("LG_DEV_CodeArt")
    var lGDEVCodeArt: String = "",

    @ColumnInfo(name = "LG_DEV_Qte")
    @SerialName("LG_DEV_Qte")
    var lGDEVQte: String? = "",

    @SerialName("LG_DEV_Unite")
    @ColumnInfo(name = "LG_DEV_Unite")
    var lGDEVUnite: String? = "",

    @SerialName("LG_DEV_PUHT")
    @ColumnInfo(name = "LG_DEV_PUHT")
    var lGDEVPUHT: String? = "",

    @SerialName("LG_DEV_Tva")
    @ColumnInfo(name = "LG_DEV_Tva")
    var lGDEVTva: String? = "",

    @SerialName("LG_DEV_Netht")
    @ColumnInfo(name = "LG_DEV_Netht")
    var lGDEVNetht: String? = "",

    @SerialName("LG_DEV_Remise")
    @ColumnInfo(name = "LG_DEV_Remise")
    var lGDEVRemise: String? = "",

    @SerialName("LG_DEV_station")
    @ColumnInfo(name = "LG_DEV_station")
    var lGDEVStation: String? = "",

    @SerialName("LG_DEV_User")
    @ColumnInfo(name = "LG_DEV_User")
    var lGDEVUser: String? = "",

    @ColumnInfo(name = "LG_DEV_NumOrdre")
    @SerialName("LG_DEV_NumOrdre")
    var lGDEVNumOrdre : Int = 0,

    @SerialName("LG_DEV_Tarif")
    @ColumnInfo(name = "LG_DEV_Tarif")
    var lGDEVTarif: String? = "",

    @SerialName("LG_DEV_QtePiece")
    @ColumnInfo(name = "LG_DEV_QtePiece")
    var lGDEVQtePiece: String? = "",

    @SerialName("LG_DEV_export")
    @ColumnInfo(name = "LG_DEV_export")
    var lGDEVExport: String? = "",

    @SerialName("LG_DEV_DDm")
    @ColumnInfo(name = "LG_DEV_DDm")
    var lGDEVDDm: String? = "",

    @SerialName("LG_DEV_MntTTC")
    @ColumnInfo(name = "LG_DEV_MntTTC")
    var lGDEVMntTTC: String? = "",

    @SerialName("LG_DEV_MntHT")
    @ColumnInfo(name = "LG_DEV_MntHT")
    var lGDEVMntHT: String? = "",

    @SerialName("LG_DEV_PUTTC")
    @ColumnInfo(name = "LG_DEV_PUTTC")
    var lGDEVPUTTC: String? = "",

    @SerialName("LG_DEV_TauxFodec")
    @ColumnInfo(name = "LG_DEV_TauxFodec")
    var lGDEVTauxFodec: String? = "",

    @SerialName("LG_DEV_TauxDc")
    @ColumnInfo(name = "LG_DEV_TauxDc")
    var lGDEVTauxDc: String? = "",

    @SerialName("LG_DEV_MntBrutHT")
    @ColumnInfo(name = "LG_DEV_MntBrutHT")
    var lGDEVMntBrutHT: String? = "",

    @SerialName("LG_DEV_MntFodec")
    @ColumnInfo(name = "LG_DEV_MntFodec")
    var lGDEVMntFodec: String? = "",

    @SerialName("LG_DEV_MntDc")
    @ColumnInfo(name = "LG_DEV_MntDc")
    var lGDEVMntDc: String? = "",

    @ColumnInfo(name = "LG_DEV_MntTva")
    @SerialName("LG_DEV_MntTva")
    var lGDEVMntTva: String? = "",

    @SerialName("LG_DEV_QteGratuite")
    @ColumnInfo(name = "LG_DEV_QteGratuite")
    var lGDEVQteGratuite: String? = "",


    @SerialName("LG_DEV_NumSerie")
    @ColumnInfo(name = "LG_DEV_NumSerie")
    var lGDevNumSerie: String? = "",


    @SerialName("LG_DEV_CMarq")
    @ColumnInfo(name = "LG_DEV_CMarq")
    var lGDEVCMarq: String? = "",




    @SerialName("LG_DEV_Note")
    @ColumnInfo(name = "LG_DEV_Note")
    val lgDEVNote: String? = "",


    @SerialName("LG_DEV_N_DEV_IN")
    @ColumnInfo(name = "LG_DEV_N_DEV_IN")
    val lgDevNDevIN: String? = "",



    @SerialName("codeLigne")
    @ColumnInfo(name = "codeLigne")
    @Transient
    var codeLigne: String? = "",

    @SerialName("msgLigne")
    @ColumnInfo(name = "msgLigne")
    @Transient
    var msgLigne: String? = "",

    @SerialName("selectedPriceCategory")
    @ColumnInfo(name = "selectedPriceCategory")
    @Transient
    var selectedPriceCategory: String = ""




): BaseModel()
