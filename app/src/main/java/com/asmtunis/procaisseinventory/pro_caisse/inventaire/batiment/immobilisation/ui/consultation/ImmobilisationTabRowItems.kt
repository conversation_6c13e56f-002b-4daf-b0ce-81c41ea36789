package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.consultation

import androidx.compose.ui.text.input.TextFieldValue
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.selection_ajout_patrimoine.SelectPatrimoineViewModel
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.utils.StringUtils.nbrTitleTabs
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import com.asmtunis.procaisseinventory.data.marque.domaine.Marque
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.TypePat
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.Immobilisation
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.view_model.InventaireBatimentViewModel
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.view_model.InventaireViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.TabRowItem


//tabRowItems: List<TabRowItem>


fun getTabRowImmobilisationItems(
    articleMap: Map<String, Article>,
    imageList: List<ImagePieceJoint>,
    invPatrimoineList: Map<BonCommande, List<LigneBonCommande>>,
    depOutListNotFiltred: Map<BonCommande, List<LigneBonCommande>>,
    depInListNotFiltred: Map<BonCommande, List<LigneBonCommande>>,
    inventaireListNotFiltred: Map<BonCommande, List<LigneBonCommande>>,
    affectationListNotFiltred: Map<BonCommande, List<LigneBonCommande>>,
    filteredDepOutList: Map<BonCommande, List<LigneBonCommande>>,
    filteredDepInList: Map<BonCommande, List<LigneBonCommande>>,
    filteredInventaireList: Map<BonCommande, List<LigneBonCommande>>,
    filteredAffectationList: Map<BonCommande, List<LigneBonCommande>>,
    invPatViewModel: InventaireViewModel,
    selectPatrimoineVM: SelectPatrimoineViewModel,
    batimentViewModel: InventaireBatimentViewModel,
    navigate: (route: Any) -> Unit,
    setCodM: (String)-> Unit,
    selectedBaseconfig: BaseConfig,
    searchTextState: TextFieldValue,
    immobilisationList: List<Immobilisation>,
    isConnected: Boolean,
    isRefreshing: Boolean,
    getImmobilisation: (BaseConfig) -> Unit,
    marqueList: List<Marque>,
    getClientName: (cltName: String?, cltCode: String?) -> String
): List<TabRowItem> = listOf(
    TabRowItem(
        title = TypePat.AFFECTATION.typePat  + nbrTitleTabs(allSize = affectationListNotFiltred.size, filteredSize = filteredAffectationList.size),
        id = TypePat.AFFECTATION.typePat,
        screen = {
            InvBatimentList(
                navigate = { navigate(it) },
                articleMap = articleMap,
                imageList = imageList,
                invPatrimoineList = invPatrimoineList,
                batimentViewModel = batimentViewModel,
                marqueList = marqueList,
                setCodM = { setCodM(it) },
                immobilisationList = immobilisationList,
                selectPatrimoineVM = selectPatrimoineVM,
                selectedBaseconfig = selectedBaseconfig,
                isConnected = isConnected,
                isRefreshing = isRefreshing,
                searchTextState = searchTextState,
                getImmobilisation = { getImmobilisation(selectedBaseconfig) },
                invPatrimoineViewModel = invPatViewModel,
                invPatList = filteredAffectationList,
                getClientName = { cltName, cltCode -> getClientName(cltName, cltCode) },
            )
        },
        // icon = Icons.Rounded.Place,
    ),
    TabRowItem(
        title =
        TypePat.INVENTAIRE.typePat + nbrTitleTabs(allSize = inventaireListNotFiltred.size, filteredSize = filteredInventaireList.size),
        id = TypePat.INVENTAIRE.typePat,
        screen = {
            InvBatimentList(
                navigate = { navigate(it) },
                articleMap = articleMap,
                imageList = imageList,
                invPatrimoineList = invPatrimoineList,
                batimentViewModel = batimentViewModel,
                setCodM = { setCodM(it) },
                immobilisationList = immobilisationList,
                marqueList = marqueList,
                selectPatrimoineVM = selectPatrimoineVM,
                invPatrimoineViewModel = invPatViewModel,
                selectedBaseconfig = selectedBaseconfig,
                isConnected = isConnected,
                isRefreshing = isRefreshing,
                searchTextState = searchTextState,
                getImmobilisation = { getImmobilisation(selectedBaseconfig) },
                invPatList = filteredInventaireList,
                getClientName = { cltName, cltCode -> getClientName(cltName, cltCode) },
            )
        },
    ),
    TabRowItem(
        title =
        TypePat.DEP_IN.typePat + nbrTitleTabs(allSize = depInListNotFiltred.size, filteredSize = filteredDepInList.size),
        id = TypePat.DEP_IN.typePat,
        screen = {
            InvBatimentList(
                navigate = { navigate(it) },
                articleMap = articleMap,
                imageList = imageList,
                invPatrimoineList = invPatrimoineList,
                batimentViewModel = batimentViewModel,
                setCodM = { setCodM(it) },
                immobilisationList = immobilisationList,
                marqueList = marqueList,
                selectPatrimoineVM = selectPatrimoineVM,
                selectedBaseconfig = selectedBaseconfig,
                isConnected = isConnected,
                getImmobilisation = { getImmobilisation(selectedBaseconfig) },
                invPatrimoineViewModel = invPatViewModel,
                invPatList = filteredDepInList,
                searchTextState = searchTextState,
                isRefreshing = isRefreshing,
                getClientName = { cltName, cltCode -> getClientName(cltName, cltCode) },
            )
        },
        //  icon = Icons.Rounded.Star,
    ),
    TabRowItem(
        title = TypePat.DEP_OUT.typePat + nbrTitleTabs(allSize = depOutListNotFiltred.size, filteredSize = filteredDepOutList.size),
        id = TypePat.DEP_OUT.typePat,
        screen = {
            InvBatimentList(
                navigate = { navigate(it) },
                articleMap = articleMap,
                imageList = imageList,
                invPatrimoineList = invPatrimoineList,
                batimentViewModel = batimentViewModel,
                setCodM = { setCodM(it) },
                immobilisationList = immobilisationList,
                marqueList = marqueList,
                selectPatrimoineVM = selectPatrimoineVM,
                selectedBaseconfig = selectedBaseconfig,
                isConnected = isConnected,
                getImmobilisation = { getImmobilisation(selectedBaseconfig) },
                typePat = TypePat.DEP_OUT.typePat,
                invPatrimoineViewModel = invPatViewModel,
                invPatList = filteredDepOutList,
                searchTextState = searchTextState,
                isRefreshing = isRefreshing,
                getClientName = { cltName, cltCode -> getClientName(cltName, cltCode) },
            )
        },
        //  icon = Icons.Rounded.Star,
    ),
)