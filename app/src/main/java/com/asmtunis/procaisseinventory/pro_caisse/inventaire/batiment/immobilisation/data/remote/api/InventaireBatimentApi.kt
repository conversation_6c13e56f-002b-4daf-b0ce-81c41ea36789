package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.remote.api

import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.BatimentCheckResponse
import kotlinx.coroutines.flow.Flow


interface InventaireBatimentApi {
    suspend fun affectCodeBareBatiment(baseConfig: String): Flow<DataResult<BatimentCheckResponse>>

}