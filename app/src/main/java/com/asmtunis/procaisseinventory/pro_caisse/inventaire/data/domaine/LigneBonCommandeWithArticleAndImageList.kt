package com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.domaine

import androidx.room.Embedded
import androidx.room.Relation
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class LigneBonCommandeWithArticleAndImageListDEPRECATED(
    @Embedded
    @SerialName("ligneBonCommande")
    var ligneBonCommande: LigneBonCommande? = null,

    @Relation(
        parentColumn = "LG_DEV_CodeArt",
        entityColumn = "ART_CodeBar"
    )
    @SerialName("article")
    var article: Article? = null,

    @Relation(
        parentColumn = "LG_DEV_NumSerie",
        entityColumn = "VCNumSerie"
    )
    @SerialName("imageList")
    var imageList: List<ImagePieceJoint>? = null,


)
