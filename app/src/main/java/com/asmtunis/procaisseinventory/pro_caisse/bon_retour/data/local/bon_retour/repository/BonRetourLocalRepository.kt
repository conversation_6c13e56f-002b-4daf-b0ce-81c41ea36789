package com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.local.bon_retour.repository

import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.BonRetour
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.BonRetourWithClient
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.LigneBonRetour
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.LigneBonRetourWithArticle
import kotlinx.coroutines.flow.Flow

interface BonRetourLocalRepository {
    fun upsertAll(value: List<BonRetour>)
    fun upsert(value: BonRetour)

   fun notSynced(): Flow<Map<BonRetour, List<LigneBonRetour>>?>

    fun setSynced(bonRetourNum : String, bonRetourNumM: String)
    fun deleteAll()

    fun deleteByCodeM(
        code: String,
        exercice: String,
    )

    fun getNewCode(prefix: String): Flow<String>

    fun getAll(station : String): Flow<Map<BonRetour, List<LigneBonRetour>>>
    fun getByClient(codeClient: String): Flow<Map<BonRetour, List<LigneBonRetourWithArticle>>>

    fun filterByCodeFournisseur(searchString: String,  sortBy: String, isAsc: Int, station : String): Flow<Map<BonRetourWithClient, List<LigneBonRetourWithArticle>>>
    fun filterByNomFournisseur(searchString: String,  sortBy: String, isAsc: Int, station : String): Flow<Map<BonRetourWithClient, List<LigneBonRetourWithArticle>>>
    fun filterByNumero(searchString: String,  sortBy: String, isAsc: Int, station : String): Flow<Map<BonRetourWithClient, List<LigneBonRetourWithArticle>>>
    fun getAllFiltred(isAsc: Int,  sortBy: String, station : String): Flow<Map<BonRetourWithClient, List<LigneBonRetourWithArticle>>>

}