package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.deplacement_out

import NavDrawer
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.AccountBalance
import androidx.compose.material.icons.filled.AddHomeWork
import androidx.compose.material.icons.filled.Home
import androidx.compose.material3.DrawerValue
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.rememberDrawerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.connectivity.location.LocationViewModule
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.ChooseSiteFinancierRoute
import com.asmtunis.procaisseinventory.core.navigation.ZoneConsomationDetailRoute
import com.asmtunis.procaisseinventory.core.navigation.ZoneConsomationRoute
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.Constant
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.Immobilisation
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.view_model.InventaireBatimentViewModel
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.FilterContainer
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.SearchSectionComposable
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.SyncProcaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncSharedViewModels
import com.simapps.ui_kit.ModifiersUtils.floatingBtnIsVisible
import com.simapps.ui_kit.custom_cards.ItemDetail

@Composable
fun ChooseSocieteScreen(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    batimentViewModel: InventaireBatimentViewModel,
    settingViewModel: SettingViewModel,
    mainViewModel: MainViewModel,
    barCodeViewModel: BarCodeViewModel,
    dataViewModel: DataViewModel,
    locationViewModule: LocationViewModule,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    networkViewModel: NetworkViewModel,

    syncSharedViewModels: SyncSharedViewModels,
    syncProcaisseViewModels: SyncProcaisseViewModels,
    syncInventoryViewModel: SyncInventoryViewModel,

    ) {
    val drawer = rememberDrawerState(initialValue = DrawerValue.Closed)
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val snackbarHostState = remember { SnackbarHostState() }
    val listState = rememberLazyListState()

    val displayAllBatiment = batimentViewModel.displayAffectedBatiment

    val allBatimentListstate = batimentViewModel.allBatimentListstate

    // else batimentViewModel.state
    val listOrder = allBatimentListstate.listOrder
    val filterList = context.resources.getStringArray(R.array.zone_consomation_filter)
    val listFilter = allBatimentListstate.filter

    val isVisible = floatingBtnIsVisible(listeSize = allBatimentListstate.lists.size, canScrollForward = listState.canScrollForward)

    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig

    LaunchedEffect(key1 = batimentViewModel.searchTextState.text, key2 = allBatimentListstate.lists, key3 = allBatimentListstate.filter) {
        batimentViewModel.filterZoneConsommation(allBatimentListstate)
    }

    LaunchedEffect(key1 = Unit) {
        batimentViewModel.onDisplayAffectedBatimentChange(false)
        batimentViewModel.onTyEmpImNomChange(Constant.SOCIETE)
        batimentViewModel.onSelectedSiteFinancierChange(Immobilisation())
        batimentViewModel.onSelectedSociteChange(Immobilisation())

        batimentViewModel.filterZoneConsommation(allBatimentListstate)
    }

    val density = LocalDensity.current

    NavDrawer(
        navigate = { navigate(it) },
        drawer = drawer,
        navDrawerViewmodel = navigationDrawerViewModel,
        getProCaisseDataViewModel = getProCaisseDataViewModel,
        getProInventoryDataViewModel = getProInventoryDataViewModel,
        getSharedDataViewModel = getSharedDataViewModel,
        networkViewModel = networkViewModel,
        dataViewModel = dataViewModel,
        mainViewModel = mainViewModel,
        syncInventoryViewModel = syncInventoryViewModel,
        syncSharedViewModels = syncSharedViewModels,
        syncProcaisseViewModels = syncProcaisseViewModels,
        settingViewModel = settingViewModel
    ) {
        Scaffold(
            snackbarHost = { SnackbarHost(snackbarHostState) },
            topBar = {
                AppBar(
                    baseConfig = selectedBaseconfig,
                    isConnected = networkViewModel.isConnected,
                    onNavigationClick = { navigate(ZoneConsomationDetailRoute) },
                    showNavIcon = !batimentViewModel.showSearchView,
                    navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                    title = batimentViewModel.tyEmpImNom,
                    titleVisibilty = !batimentViewModel.showSearchView,
                    actions = {
                        SearchSectionComposable(
                            label = context.getString(
                                    R.string.filter_by,
                                    when (listFilter) {
                                        is ListSearch.FirstSearch -> filterList[0]
                                        is ListSearch.SecondSearch -> filterList[1]
                                        else -> filterList[2]
                                    },
                                ),
                            searchVisibility = batimentViewModel.showSearchView,
                            searchTextState = batimentViewModel.searchTextState,
                            onSearchValueChange = {
                                batimentViewModel.onSearchValueChange(TextFieldValue(it))
                            },
                            onShowSearchViewChange = {
                                batimentViewModel.onShowSearchViewChange(it)
                            },
                            onShowCustomFilterChange = {
                                batimentViewModel.onShowCustomFilterChange(it)
                            },
                        )
                        AnimatedVisibility(
                            visible = !batimentViewModel.showSearchView,
                            enter = fadeIn() + slideInVertically(),
                            exit = fadeOut() + slideOutVertically()
                        ) {
                            IconButton(
                                onClick = { navigate(ZoneConsomationRoute) }
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Home,
                                    contentDescription = stringResource(
                                        id = R.string.quitter
                                    )
                                )
                            }
                        }
                    },
                )
            },
            //    containerColor = colorResource(id = R.color.black),
            floatingActionButton = {
              /*  AnimatedVisibility(
                    visible = batimentViewModel.selectedSocite.cLICode.isNotEmpty() ,
                    enter = slideInVertically {
                        with(density) { 40.dp.roundToPx() }
                    } + fadeIn(),
                    exit = fadeOut(
                        animationSpec = keyframes {
                            this.durationMillis = 120
                        }
                    )
                ) {*/
            },
        ) { padding ->

            Column(
                verticalArrangement = Arrangement.Top,
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier =
                    Modifier
                        //  .background(colorResource(id = R.color.black))
                        .fillMaxWidth()
                        .padding(padding),
            ) {
                if (batimentViewModel.showCustomFilter) {
                    FilterContainer(
                        filterList = filterList,
                        listFilter = listFilter,
                        listOrder = listOrder,
                        orderList = context.resources.getStringArray(R.array.zone_consomation_order),
                        onShowCustomFilterChange = {
                            batimentViewModel.onShowCustomFilterChange(false)
                        },
                        onEvent = {
                            batimentViewModel.onEvent(event = it)
                        },
                    )
                }

                AnimatedVisibility(
                    visible = batimentViewModel.selectedSocite.cLICode.isNotEmpty(),
                    enter =
                        slideInVertically {
                            with(density) { 40.dp.roundToPx() }
                        } + fadeIn(),
                    exit =
                        fadeOut(
                            animationSpec =
                                keyframes {
                                    this.durationMillis = 120
                                },
                        ),
                ) {
                    ItemDetail(
                        //  title = stringResource(id = R.string.societe),
                        title = "Societé Destination",
                        dataText = batimentViewModel.selectedSocite.cLINomPren,
                        icon = Icons.Default.AccountBalance,
                        onLongPress = {
                            batimentViewModel.onSelectedSociteChange(Immobilisation())
                        },
                    )

                    Spacer(modifier = Modifier.height(6.dp))
                }

                if (batimentViewModel.allBatimentListstate.lists.isNotEmpty()) {
                    ImmoList(
                        navigate = { navigate(it) },
                        listState = listState,
                        batimentViewModel = batimentViewModel,
                        filteredZoneConsomation = batimentViewModel.allBatimentListstate.lists,
                    )
                } else {
                    if (getProCaisseDataViewModel.immobilisationState.loading) {
                        LottieAnim(lotti = R.raw.loading, size = 250.dp)
                    } else {
                        batimentViewModel.filterZoneConsommation(allBatimentListstate)
                    }
                    LottieAnim(lotti = R.raw.emptystate, size = 250.dp)
                }
            }
        }
    }
}

@Composable
fun ImmoList(
    navigate: (route: Any) -> Unit,
    listState: LazyListState,
    batimentViewModel: InventaireBatimentViewModel,
    filteredZoneConsomation: List<Immobilisation>,
) {
    // val state = remember { batimentViewModel.state.value }
    val state = batimentViewModel.allBatimentListstate

    val context = LocalContext.current

    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        state = listState,
    ) {
        items(
            count = filteredZoneConsomation.size,
            key = {
                filteredZoneConsomation[it].cLICode
            },
        ) { index ->

            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Start,
                modifier =
                    Modifier
                        .wrapContentHeight()
                        .fillMaxWidth()
                        .clickable {
                            if (batimentViewModel.selectedSocite.cLICode == filteredZoneConsomation[index].cLICode) {
                                batimentViewModel.onSelectedSociteChange(Immobilisation())
                            } else {
                                batimentViewModel.onSelectedSociteChange(filteredZoneConsomation[index])
                            }

                            batimentViewModel.resetSearch()
                            navigate(ChooseSiteFinancierRoute)
                        },
                // .background("#063041".color)
            ) {
                Column(
                    modifier =
                        Modifier
                            //  .background(colorResource(id =if(state.lists[index].solde.toDouble() < 0.0) R.color.teal_700 else R.color.white ))
                            .wrapContentSize(),
                    // .padding(padding) ,
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    if (!filteredZoneConsomation[index].isSync) {
                        LottieAnim(lotti = R.raw.connection_error, size = 80.dp)
                    } else {
                        Icon(
                            imageVector =
                               /* if (batimentViewModel.selectedSocite.cLICode == filteredZoneConsomation[index].cLICode) {
                                    Icons.Default.Check
                                } else */
                                Icons.Default.AddHomeWork,
                            contentDescription = "",
                            modifier =
                                Modifier
                                    .size(80.dp),
                            // .padding(5.dp)
                        )
                    }

                    Text(
                        modifier = Modifier.width(95.dp),
                        text = filteredZoneConsomation[index].cLICode,
                        fontSize = MaterialTheme.typography.bodySmall.fontSize,
                        fontWeight = MaterialTheme.typography.bodySmall.fontWeight,
                        maxLines = 1,
                        textAlign = TextAlign.Center,
                    )
                }

                Column(
                    modifier =
                        Modifier
                            //  .background(colorResource(id =if(state.lists[index].solde.toDouble() < 0.0) R.color.teal_700 else R.color.white ))
                            .wrapContentSize(),
                    // .padding(padding) ,
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.Start,
                ) {
                    Text(
                        text = filteredZoneConsomation[index].cLINomPren ?: "N/A",
                        fontSize = MaterialTheme.typography.titleMedium.fontSize,
                        fontWeight = MaterialTheme.typography.titleMedium.fontWeight,
                        maxLines = 2,
                        //   color = Color.Black
                    )
                }
            }

            //  Divider()
        }
    }
}
