package com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine

import androidx.room.Embedded
import androidx.room.Relation
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Serializable
data class LigneBonCommandeWithArticle (
    @Embedded
    @SerialName("ligneBonCommande")
    var ligneBonCommande: LigneBonCommande? = null,

    @Relation(
        parentColumn = "LG_DEV_CodeArt",
        entityColumn = "ART_Code"
    )
    @SerialName("article")
    var article: Article? = null,


    )