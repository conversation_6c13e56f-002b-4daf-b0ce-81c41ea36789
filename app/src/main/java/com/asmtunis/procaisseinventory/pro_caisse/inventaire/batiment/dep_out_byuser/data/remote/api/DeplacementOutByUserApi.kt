package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.data.remote.api

import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.data.domaine.DeplacementOutByUser
import kotlinx.coroutines.flow.Flow


interface DeplacementOutByUserApi {
        suspend fun getAllDeplacememntOutByUser(baseConfig: String): Flow<DataResult<List<DeplacementOutByUser>>>
}


