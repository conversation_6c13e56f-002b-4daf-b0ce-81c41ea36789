package com.asmtunis.procaisseinventory.pro_caisse.bon_retour.view_model

import android.util.Log
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.text.input.TextFieldValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.articles.ArticleOpeartions
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.domaine.SelectedArticle
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.data.exercice.domaine.Exercice
import com.asmtunis.procaisseinventory.data.station.domaine.StationStockArticle
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.BonRetour
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.BonRetourWithClient
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.LigneBonRetour
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.LigneBonRetourWithArticle
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.filter.BonRetourFilterListState
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.ListEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.ListOrder
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.OrderType
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import com.simapps.ui_kit.utils.getCurrentDateTime
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.launch
import javax.inject.Inject


@HiltViewModel
class BonRetourViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    private val proCaisseLocalDb: ProCaisseLocalDb
) : ViewModel() {









    /* fun createBonRetourNum(){
 bonRetourNum = prefixBonRetour + "_" + BuildConfig.VERSION_NAME + "_" + bonRetourNewCode
     }


   private  var bonRetourNewCode by mutableStateOf("")

   private  fun getBonRetourNewCode() {
         viewModelScope.launch {
             proCaisseLocalDb.bonRetour.getNewCode(prefixBonRetour).collect{
                 bonRetourNewCode = it

                 createBonRetourNum()
             }
         }

     }



     private  var prefixBonRetour by mutableStateOf("")

     fun getPrefixBonRetour() {
         viewModelScope.launch {
             proCaisseLocalDb.prefix.getOneById("Bon_Retour").collect{

                 if(it==null) return@collect
                 prefixBonRetour = it.pREPrefixe?:"BR"

                 getBonRetourNewCode()
             }
         }

     }
     */
    var bonRetourList: Map<BonRetour, List<LigneBonRetourWithArticle>> by mutableStateOf(emptyMap())
        private set
    fun getBonRetourListByClient(codeClient: String) {
        viewModelScope.launch {
            proCaisseLocalDb.bonRetour.getByClient(codeClient).collect {
                bonRetourList = it
            }
        }
    }

    var showSearchView: Boolean by mutableStateOf(false)
        private set
    fun onShowSearchViewChange(value: Boolean) {
        showSearchView = value
    }

    var showCustomFilter: Boolean by mutableStateOf(false)
        private set
    fun onShowCustomFilterChange(value: Boolean) {
        showCustomFilter = value
    }

    var showCustomModalBottomSheet: Boolean by mutableStateOf(false)
        private set
    fun onShowCustomModalBottomSheetChange(value: Boolean) {
        showCustomModalBottomSheet = value
    }


    var searchTextState: TextFieldValue by mutableStateOf(TextFieldValue(""))
        private set
    fun onSearchValueChange(value: TextFieldValue) {
        searchTextState = value
    }





    var selectedLigneBonRetour: LigneBonRetour by mutableStateOf(LigneBonRetour())
        private set
    fun onSelectedLigneBonRetourChange(value: LigneBonRetour) {
        selectedLigneBonRetour = value
    }
    var selectedListLgBonRetour = mutableStateListOf<LigneBonRetour>()
        private set

    var selectedListLgBonRetourWithArticle = mutableStateListOf<LigneBonRetourWithArticle>()
        private set
    var selectedBonRetour: BonRetour by mutableStateOf(BonRetour())
        private set

    var selectedBonRetourWithClient: BonRetourWithClient by mutableStateOf(BonRetourWithClient())
        private set
    fun onSelectedBonRetourChange(value: Map<BonRetourWithClient, List<LigneBonRetourWithArticle>>) {
        value.forEach { (key, value) ->
            run {
                selectedBonRetour = key.bonRetour?: BonRetour()
                selectedBonRetourWithClient = key
                selectedListLgBonRetour.addAll(value.map { it.ligneBonRetour?: LigneBonRetour() })
                selectedListLgBonRetourWithArticle.addAll(value)
            }
        }

    }


    fun restBonRetour(){
        selectedListLgBonRetour.clear()
        selectedListLgBonRetourWithArticle.clear()
        selectedBonRetour = BonRetour()
    }






    var bonRetourListstate: BonRetourFilterListState by mutableStateOf(BonRetourFilterListState())
        private set






    /*  var bonTransfertPrefix: Prefixe by mutableStateOf(Prefixe())
         private set
       fun getBonRetourPrefix(utilisateur: Utilisateur) {
          viewModelScope.launch {
              proCaisseLocalDb.prefix.getOneById("bon_transfert").collect {
                  if(it!=null){
                      bonTransfertPrefix = it
  
                      if(it.pREPrefixe!=null) generateBonEntreeNum(it.pREPrefixe!!,utilisateur)
                  }
  
              }
          }
      }
  
      var bonTransfertGeneratedNum: String by mutableStateOf("")
          private set
    fun generateBonEntreeNum(prefix : String,utilisateur: Utilisateur) {
          val stationUtilisateur = utilisateur.Station
          val userID = utilisateur.Code_Ut
          viewModelScope.launch {
              proCaisseLocalDb.bonRetour.getNewCode(prefix).collect {
                  bonTransfertGeneratedNum =prefix + it
              }
          }
      }*/



    fun deleteBonRetourAndItsLines(
        listStationStockArticl: Map<String, StationStockArticle>,
        updateQtePerStation: (newQteStation: String, newSartQteDeclare: String, codeArticle: String, codeStation: String) -> Unit,
        updateArtQteStock: (newQteAllStations: String, newQteStation: String, codeArticle: String) -> Unit
    ) {
        viewModelScope.launch(dispatcher) {
            val bonCommande = selectedBonRetourWithClient.bonRetour ?: return@launch

            val codeM = bonCommande.bORNumero


            val listLgBc = selectedListLgBonRetourWithArticle

            for (lgBc in listLgBc) {
                Log.d("ffggvvffd", "lgBc.article "+lgBc.article)
                val article = lgBc.article?: break

                val ligneBonCommande = lgBc.ligneBonRetour
                val qty = stringToDouble(ligneBonCommande?.lIGBonEntreeQte)


                ArticleOpeartions.updateArticleQty(
                    operation = Globals.MINUS,
                    quantity = qty,
                    article = article,
                    updateArtQteStock = { newQteAllStations, newQteStation, codeArticle ->
                        updateArtQteStock(
                            newQteAllStations,
                            newQteStation,
                            codeArticle
                        )
                    }
                )
               // val stationStockArticle = listStationStockArticl.firstOrNull { it.sARTCodeSatation == bonCommande.bORStation && it.sARTCodeArt == ligneBonCommande?.lIGBonEntreeCodeArt }
                val stationStockArticle = listStationStockArticl[ligneBonCommande?.lIGBonEntreeCodeArt + bonCommande.bORStation]

                ArticleOpeartions.getStationStockArticle(
                    opeartion = Globals.MINUS,
                    codeStation = bonCommande.bORStation!!,
                    codeArticle = ligneBonCommande?.lIGBonEntreeCodeArt!!,
                    stationStockArticle = stationStockArticle,
                    qty = qty,
                    updateQtePerStation = { newQteStation, newSartQteDeclare, codeArticle, codeStation ->
                        updateQtePerStation(
                            newQteStation,
                            newSartQteDeclare,
                            codeArticle,
                            codeStation
                        )
                    }
                )


            }



            proCaisseLocalDb.ligneBonRetour.deleteByCodeM(code = codeM, exercice = bonCommande.bORExercice?: "")
            proCaisseLocalDb.bonRetour.deleteByCodeM(code = codeM, exercice = bonCommande.bORExercice?: "")

        }
    }

    fun saveBonRetour(bonRetour: BonRetour) {
        viewModelScope.launch(dispatcher) {
            proCaisseLocalDb.bonRetour.upsert(bonRetour)
        }
    }


    fun saveListLigneBonRetour(lgbonRetour: List<LigneBonRetour>) {
        viewModelScope.launch(dispatcher) {
            proCaisseLocalDb.ligneBonRetour.upsertAll(lgbonRetour)
        }
    }

    private fun saveLigneBonRetour(lgbonRetour: LigneBonRetour) {
        viewModelScope.launch(dispatcher) {
            proCaisseLocalDb.ligneBonRetour.upsert(lgbonRetour)
        }
    }
    fun onEvent(event: ListEvent, utilisateur: Utilisateur) {
        when (event) {
            is ListEvent.Order -> {
                if (bonRetourListstate.listOrder::class == event.listOrder::class &&
                    bonRetourListstate.listOrder.orderType == event.listOrder.orderType
                ) {
                    return
                }
                bonRetourListstate = bonRetourListstate.copy(
                    listOrder = event.listOrder
                )
                filterBonRetour(bonRetourListstate, utilisateur = utilisateur)
            }
            is ListEvent.Delete -> TODO()
            is ListEvent.Restore -> TODO()

            is ListEvent.ListSearch -> {
                bonRetourListstate = bonRetourListstate.copy(
                    search = event.listSearch
                )

                filterBonRetour(bonRetourListstate, utilisateur = utilisateur)
            }

            is ListEvent.FirstCustomFilter -> {
                //TODO IF WE ADD FILTERS
            }

            is ListEvent.SecondCustomFilter -> {
                //TODO IF WE ADD FILTERS
            }
            is ListEvent.ThirdCustomFilter -> TODO()

        }

    }
  //  var getBonRetourtJob: Job = Job()
  fun filterBonRetour(
      bonRetourFilterListState: BonRetourFilterListState,
      utilisateur: Utilisateur
  ) {
      val searchedText = searchTextState.text
      val searchValue = bonRetourFilterListState.search

      // Determine sorting order
      val isAscending = when (bonRetourFilterListState.listOrder.orderType) {
          is OrderType.Ascending -> 1
          is OrderType.Descending -> 2
      }

      // Determine sorting column
      val sortBy = when (bonRetourFilterListState.listOrder) {
          is ListOrder.Title -> "BOR_Numero"
          is ListOrder.Date -> "DDmM"
          is ListOrder.Third -> "BOR_Mnt_TTC"
      }

      viewModelScope.launch {
          val result = if (searchedText.isEmpty()) {
              proCaisseLocalDb.bonRetour.getAllFiltred(
                  isAsc = isAscending,
                  sortBy = sortBy,
                  station = utilisateur.Station
              )
          } else {
              when (searchValue) {
                  is ListSearch.FirstSearch -> proCaisseLocalDb.bonRetour.filterByNumero(
                      searchString = searchedText,
                      sortBy = sortBy,
                      isAsc = isAscending,
                      station = utilisateur.Station
                  )
                  is ListSearch.SecondSearch -> proCaisseLocalDb.bonRetour.filterByNomFournisseur(
                      searchString = searchedText,
                      sortBy = sortBy,
                      isAsc = isAscending,
                      station = utilisateur.Station
                  )
                  is ListSearch.ThirdSearch -> proCaisseLocalDb.bonRetour.filterByCodeFournisseur(
                      searchString = searchedText,
                      sortBy = sortBy,
                      isAsc = isAscending,
                      station = utilisateur.Station
                  )
                  else -> return@launch
              }
          }

          result.collect { setBnRetourList(it) }
      }
  }

    private fun setBnRetourList(bonRetour: Map<BonRetourWithClient, List<LigneBonRetourWithArticle>>) {
        bonRetourListstate = bonRetourListstate.copy(
            lists = bonRetour
        )
    }






    fun saveNewBonRetour(
        clientByCode: Client,
        utilisateur: Utilisateur,
        codeM: String,
        exerciceList: List<Exercice>,
        listStationStockArticl: Map<String, StationStockArticle>,
        totPriceWithoutDicount: Double,
        selectedArticleList: List<SelectedArticle>,
        updateQtePerStation: (newQteStation: String, newSartQteDeclare: String, codeArticle: String, codeStation: String) -> Unit,
        updateArtQteStock: (newQteAllStations: String, newQteStation: String, codeArticle: String) -> Unit
    ) {
        //val mntTTC = Calculations.totalPriceTTC(listArt = selectedArticleMobilityList)

        val mntRemise = selectedArticleList.sumOf { stringToDouble(it.mntDiscount) }

        val bonRetour = BonRetour(
            bORNumero = codeM,
            bORDate = getCurrentDateTime(),
            bORCodefrs = clientByCode.cLICode,
            bORNomfrs = clientByCode.cLINomPren,
            bORExercice = exerciceList.first().exerciceCode,
            bORNumBE = "",
            bORMntHT = selectedArticleList.sumOf { stringToDouble(it.lTPuHT) }.toString(),
            bORMntFodec = "0.0",
            bORMntRemise = mntRemise.toString().ifEmpty { "0.0" },
            bORMntMntNetHt = selectedArticleList.sumOf { stringToDouble(it.lTMtNetHT) }.toString(),
            bORMntTva = selectedArticleList.sumOf { stringToDouble(it.mntTva) }.toString(),
            bORMntTTC = totPriceWithoutDicount.toString(),
            bORStation = utilisateur.Station,
            bORType = "RetourTick",
            bORSession = "",
            bORMntAchat = "",
            observation = utilisateur.codeUt,
            //bONENTMntFodec = "",
            bONENTMntDC = "0.0"
        )

        bonRetour.status = ItemStatus.INSERTED.status
        bonRetour.isSync = false



        saveBonRetour(bonRetour = bonRetour)


        saveListLgBonRetour(
            selectedArticleList = selectedArticleList,
            utilisateur = utilisateur,
            listStationStockArticl = listStationStockArticl,
            clientByCode = clientByCode,
            codeM = codeM,
            exerciceList = exerciceList,
            updateQtePerStation = { newQteStation, newSartQteDeclare, codeArticle, codeStation ->
                updateQtePerStation(newQteStation, newSartQteDeclare, codeArticle, codeStation)

            },
            updateArtQteStock = { newQteAllStations,newQteStation,codeArticle ->
                updateArtQteStock(
                    newQteAllStations,
                    newQteStation,
                    codeArticle
                )
            }
        )
    }


    private fun saveListLgBonRetour(
        utilisateur: Utilisateur,
        listStationStockArticl: Map<String, StationStockArticle>,
        selectedArticleList: List<SelectedArticle>,
        exerciceList: List<Exercice>,
        clientByCode: Client,
        codeM: String,
        updateQtePerStation: (newQteStation: String, newSartQteDeclare: String, codeArticle: String, codeStation: String) -> Unit,
        updateArtQteStock: (newQteAllStations: String, newQteStation: String, codeArticle: String) -> Unit
    ) {

        for (i in selectedArticleList.indices) {
            val articl = selectedArticleList[i].article
            val ligneBonRetour = LigneBonRetour(
                numBonRetour = codeM,
                lIGBonEntreeDDm = getCurrentDateTime(),
                lIGBonEntreeStation = clientByCode.cLIStation,
                lIGBonEntreeCodeArt = articl.aRTCode,
                lIGBonEntreeExerc = exerciceList.first().exerciceCode,
                lIGBonEntreeQte = selectedArticleList[i].quantity,
                lIGBonEntreeUnite = articl.uNITEARTICLECodeUnite?:"",
                lIGBonEntreeMntNetHt = "-" + selectedArticleList[i].lTMtNetHT,
                lIGBonEntreeMntTTC = "-" + selectedArticleList[i].lTMtTTC,
                lIGBonEntreeMntBrutHT = "-" + selectedArticleList[i].lTMtBrutHT,
                lIGBonEntreePUHT = "-" + selectedArticleList[i].lTPuHT,
                lIGBonEntreePUTTC = "-" + selectedArticleList[i].lTPuTTC,
                 lIGBonEntreeTva = selectedArticleList[i].article.aRTTVA.toString(),
               // lIGBonEntreeTva = selectedArticleList[i].tva.tVACode,
                //lIGBonEntreeMntTva = (stringToDouble(selectedArticleList[i].lTMtTTC) - stringToDouble(selectedArticleList[i].lTMtBrutHT)).toString(),
                lIGBonEntreeMntTva = selectedArticleList[i].mntTva.toString(),
                lIGBonEntreeRemise = selectedArticleList[i].discount.ifEmpty { "0.0" },
                lIGBonEntreeUser = utilisateur.codeUt
            )


            ligneBonRetour.isSync = false
            ligneBonRetour.status = ItemStatus.INSERTED.status

            saveLigneBonRetour(ligneBonRetour)
            val qty = stringToDouble(selectedArticleList[i].quantity)
           // val stationStockArticle = listStationStockArticl.firstOrNull { it.sARTCodeSatation == utilisateur.Station && it.sARTCodeArt == articl.aRTCode }?: StationStockArticle()
            val stationStockArticle = listStationStockArticl[articl.aRTCode + utilisateur.Station]?: StationStockArticle()

            ArticleOpeartions.updateStationArticleQte(
                opeartion = Globals.ADD,
                quatity = qty,
                aRTCode = articl.aRTCode,
                codeStation = clientByCode.cLIStation,
                stationStockArticle =  stationStockArticle,
                updateQtePerStation = { newQteStation, newSartQteDeclare, codeArticle, codeStation->
                    updateQtePerStation(newQteStation, newSartQteDeclare, codeArticle, codeStation)
                }
            )

            ArticleOpeartions.updateArticleQty(
                quantity = qty,
                article = articl,
                updateArtQteStock = { newQteAllStations,newQteStation,codeArticle ->
                    updateArtQteStock(
                        newQteAllStations,
                        newQteStation,
                        codeArticle
                    )
                }
            )
        }


    }

}