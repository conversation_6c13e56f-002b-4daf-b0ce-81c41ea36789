package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.local.dao.BatimentsByUserDAO
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.local.repository.BatimentsByUserLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.local.repository.BatimentsByUserLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
class BatimentsByUserLocalModule {

    @Provides
    @Singleton
    fun provideBatimentByUserDao(
        proCaisseDataBase: ProCaisseDataBase
    ) = proCaisseDataBase.batimentsByUserDao()

    @Provides
    @Singleton
    @Named("BatimentByUser")
    fun provideBatimentByUserRepository(
        batimentByUserDAO: BatimentsByUserDAO
    ): BatimentsByUserLocalRepository = BatimentsByUserLocalRepositoryImpl(batimentsDAO = batimentByUserDAO)
}