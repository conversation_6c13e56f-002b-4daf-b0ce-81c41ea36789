package com.asmtunis.procaisseinventory.auth.login.data.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import kotlinx.coroutines.flow.Flow



@Dao
interface UtilisateurDAO {
    @get:Query("SELECT * FROM ${ProCaisseConstants.UTILISATEUR_TABLE}")
    val all: Flow<List<Utilisateur>?>


    @Query("DELETE FROM ${ProCaisseConstants.UTILISATEUR_TABLE}")
    fun deleteAll()

    @Query("SELECT * FROM ${ProCaisseConstants.UTILISATEUR_TABLE}")
    fun getUser(): Flow<Utilisateur?>


    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: Utilisateur)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(item: List<Utilisateur>)

}