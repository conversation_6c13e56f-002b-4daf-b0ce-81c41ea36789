package com.asmtunis.procaisseinventory.auth.subscribtion.screens

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.AuthViewModel
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.SplashRoute
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim

@Composable
fun WaitingLicenceActivationScreen(
    navigate: (route: Any) -> Unit,
    dataViewModel: DataViewModel,
    authViewModel: AuthViewModel,
    networkViewModel: NetworkViewModel
) {
    Scaffold { padding ->

        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            var to = ""
            val isConnected = networkViewModel.isConnected
            if (dataViewModel.getProInventorySubscribtionSent()) to = "Licence ProInventory "
            if (dataViewModel.getProcaisseSubscribtionSent()) to = "$to Licence ProCaisse Mobility"

            LottieAnim(lotti = R.raw.licence_expired)
            Spacer(modifier = Modifier.padding(top = 12.dp))

            Text(
                text = stringResource(R.string.vousAvezDemander, to),
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary,
                // modifier = Modifier.align(Alignment.CenterHorizontally),
                textAlign = TextAlign.Center,
                fontSize = 25.sp
            )
            Spacer(modifier = Modifier.padding(top = 12.dp))
            // LoadingAnimation()

            Button(
                enabled = isConnected,
                onClick = {
                    // activationViewModel.checkProCaisseLicence("c8020d0c446aa99f")
                    authViewModel.deleteAllBaseConfig()
                    dataViewModel.saveInventoryActivationState(false)
                    dataViewModel.saveProcaisseActivationState(false)

                    authViewModel.requestCheckLicense()

                    navigate(SplashRoute)
                }
            ) {
                if(!isConnected) {
                    LottieAnim(lotti = R.raw.no_connection, size = 25.dp)
                    Spacer(modifier = Modifier.width(6.dp))
                }
                Text(text = stringResource(R.string.verifierActivationLicence))
            }
        }
    }

}
