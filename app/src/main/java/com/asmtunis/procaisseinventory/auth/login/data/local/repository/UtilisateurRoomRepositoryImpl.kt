package com.asmtunis.procaisseinventory.auth.login.data.local.repository

import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.auth.login.data.local.dao.UtilisateurDAO
import kotlinx.coroutines.flow.Flow

class UtilisateurRoomRepositoryImpl(
    private val utilisateurDAO: UtilisateurDAO
) : UtilisateurRoomRepository {

    override fun upsert(value: Utilisateur) = utilisateurDAO.insert(value)
    override fun upsertAll(value: List<Utilisateur>) = utilisateurDAO.insertAll(value)

    override fun deleteAll() = utilisateurDAO.deleteAll()

    override fun getAll(): Flow<List<Utilisateur>?> = utilisateurDAO.all
    override fun getUser(): Flow<Utilisateur?> = utilisateurDAO.getUser()

}
