package com.asmtunis.procaisseinventory.auth.base_config

import android.util.Log
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.DoneOutline
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.twotone.Check
import androidx.compose.material.icons.twotone.DataObject
import androidx.compose.material.icons.twotone.NotInterested
import androidx.compose.material3.FilledTonalButton
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.windowsizeclass.WindowWidthSizeClass
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.AuthViewModel
import com.asmtunis.procaisseinventory.auth.LogoView
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.Globals.PRO_CAISSE
import com.asmtunis.procaisseinventory.core.Globals.PRO_CAISSE_MOBILITY
import com.asmtunis.procaisseinventory.core.Globals.PRO_INVENTORY
import com.asmtunis.procaisseinventory.core.Globals.PRO_INVENTORY_AUTHORIZATION_TYPE_MENU
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.LoginRoute
import com.asmtunis.procaisseinventory.core.navigation.SplashRoute
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.CustomModifiers.customWidth
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.floatingActionButtonPosition
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.dokar.sonner.ToastType
import com.dokar.sonner.ToasterState
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.custom_cards.ItemDetail
import kotlinx.serialization.json.Json

@Composable
fun BaseConfigScreen(
    navigate: (route: Any) -> Unit,
    settingViewModel: SettingViewModel,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    dataViewModel: DataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    networkViewModel: NetworkViewModel,
    authViewModel: AuthViewModel,
    mainViewModel: MainViewModel
) {
    val context = LocalContext.current

    val baseConfigList = authViewModel.baseConfigList
    val baseConfigLoadingState = authViewModel.checkLicensestate.loading
    val baseConfigErroState = authViewModel.checkLicensestate.error
    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)

    val selectedBaseconfig: BaseConfig =  dataViewModel.selectedBaseConfig


    val uiWindowState = settingViewModel.uiWindowState
    val windowSize = uiWindowState.windowSize!!//?:  WindowSizeClass(WindowWidthSizeClass.Compact, WindowHeightSizeClass.Compact)

    val isConnected = networkViewModel.isConnected

      val logo = mainViewModel.logo

    LaunchedEffect(key1 = baseConfigList) {
        authViewModel.getBaseConfigList()
    }

    LaunchedEffect(key1 = Unit) {
        if(logo != null) {
            mainViewModel.resetLogo()
        }
    }
    Scaffold(
        topBar = {
            AppBar(
                showNavIcon = false,
                title = stringResource(R.string.listBaseConfig),
                isConnected = isConnected,
            )
        },
        floatingActionButtonPosition = floatingActionButtonPosition(windowSize = windowSize),
        floatingActionButton = {
            val density = LocalDensity.current
            AnimatedVisibility(
                visible = isConnected && baseConfigList.isNotEmpty(),
                enter = slideInVertically {
                    with(density) { 40.dp.roundToPx() }
                } + fadeIn(),
                exit = fadeOut(
                    animationSpec = keyframes {
                        this.durationMillis = 120
                    }
                )
            ) {
                Column {

                    FloatingActionButton(
                        onClick = {
                                if (selectedBaseconfig != BaseConfig()) {
//                                BASE_URL = String.format(
//                                    StringUtils.validateBaseUrl(
//                                        selectedBaseconfig
//                                    ), selectedBaseconfig.adresse_ip, selectedBaseconfig.port)



                                    navigate(LoginRoute)
                                    return@FloatingActionButton
                                }
                                showToast(
                                    context = context,
                                    toaster = toaster,
                                    message = context.resources.getString(R.string.error) + "\n" + context.resources.getString(
                                        R.string.select_base_config
                                    ),
                                    type = ToastType.Error
                                )



                        }
                    ) {
                        Icon(
                            imageVector = Icons.Default.DoneOutline,
                            contentDescription = stringResource(id = R.string.add_Client_button)
                        )
                    }
                    Spacer(modifier = Modifier.height(12.dp))

                    if(isConnected) {
                        FloatingActionButton(
                            onClick = {
                                authViewModel.requestCheckLicense()
                            }
                        ) {


                            if (baseConfigLoadingState)
                                LottieAnim(lotti = R.raw.loading, size = 40.dp)
                            else

                                Icon(
                                    imageVector = Icons.Default.Refresh,
                                    contentDescription = stringResource(id = R.string.add_Client_button)
                                )
                        }
                    }


                }
            }


        }
        ) { padding ->

        when (windowSize.widthSizeClass) {
            WindowWidthSizeClass.Compact -> {

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(padding)
                        .padding(start = 9.dp, end = 9.dp),
                    verticalArrangement = Arrangement.Top,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {

                    LogoView(
                        logo = logo,
                        toaster = toaster,
                    )
                    Spacer(modifier = Modifier.height(18.dp))

                    BaseConfigContent(
                        baseConfigList = baseConfigList,
                        baseConfigLoadingState = baseConfigLoadingState,
                        isConnected = isConnected,
                        baseConfigErroState = baseConfigErroState,
                        toaster = toaster,
                        authViewModel = authViewModel,
                        dataViewModel = dataViewModel,
                        navigate = { navigate(it) },
                    )
                }

                    /* SubscribtionButton(
                         productName = Globals.PRO_CAISSE_MOBILITY,
                         isActivated = dataViewModel::getProcaisseActivationState,
                         isSubscriptionSent = dataViewModel::getProcaisseSubscribtionSent,
                         isDemo = isDemo,
                         selectedBaseconfig = selectedBaseconfig,
                         onClick =  { navigate(SubscribtionRoute) }
                     )

                     SubscribtionButton(
                         productName = Globals.PRO_INVENTORY,
                         isActivated = dataViewModel::getInventoryActivationState,
                         isSubscriptionSent =  dataViewModel::getProInventorySubscribtionSent,
                         isDemo = isDemo,
                         selectedBaseconfig = selectedBaseconfig,
                         onClick = { navigate(SubscribtionRoute) }
                     )*/


            }

            WindowWidthSizeClass.Expanded,
            WindowWidthSizeClass.Medium -> {
                Row(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(padding)
                        .padding(start = 9.dp, end = 9.dp),
                ) {
                    Column(
                        modifier = Modifier
                            .customWidth(0.45f)
                            //   .padding(padding)
                            .padding(start = 9.dp, end = 9.dp)
                            .verticalScroll(rememberScrollState()),
                        verticalArrangement = Arrangement.Top,
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        LogoView(
                            logo = logo,
                            toaster = toaster,
                        )

                    }

                    Spacer(modifier = Modifier.height(18.dp))
                    BaseConfigContent(
                        baseConfigList = baseConfigList,
                        baseConfigLoadingState = baseConfigLoadingState,
                        isConnected = isConnected,
                        baseConfigErroState = baseConfigErroState,
                        toaster = toaster,
                        authViewModel = authViewModel,
                        dataViewModel = dataViewModel,
                        navigate = { navigate(it) },
                    )
                }

            }
            else -> {
                BaseConfigContent(
                    baseConfigList = baseConfigList,
                    baseConfigLoadingState = baseConfigLoadingState,
                    isConnected = isConnected,
                    baseConfigErroState = baseConfigErroState,
                    toaster = toaster,
                    authViewModel = authViewModel,
                    dataViewModel = dataViewModel,
                    navigate = { navigate(it) },
                )
            }
        }

    }

}



@Composable
fun BaseConfigContent(
    baseConfigList: List<BaseConfig>,
    baseConfigLoadingState: Boolean,
    isConnected: Boolean,
    baseConfigErroState: String?,
    toaster: ToasterState,
    authViewModel: AuthViewModel,
    dataViewModel: DataViewModel,
    navigate: (route: Any) -> Unit,
    ) {
    if (baseConfigList.isEmpty()) {

        val lotti = if (baseConfigLoadingState) R.raw.loading else R.raw.emptystate
        LottieAnim(lotti = lotti)

        Spacer(modifier = Modifier.height(12.dp))
        Text(
            text = stringResource(id = R.string.base_config_empty),
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center,
            fontSize = 25.sp
        )

        if(baseConfigErroState != null) {
            Spacer(modifier = Modifier.height(12.dp))
            Text(
                text = baseConfigErroState.replace("*", ""),
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center,
                fontSize = 14.sp,
                maxLines = 1,
                color = MaterialTheme.colorScheme.error
            )
        }
        if (!isConnected) {
            Spacer(modifier = Modifier.height(12.dp))
            LottieAnim(lotti = R.raw.no_connection, size = 100.dp)
        }
        Spacer(modifier = Modifier.height(20.dp))
        FilledTonalButton(
            enabled = isConnected,
            onClick = {
                authViewModel.requestCheckLicense()
                navigate(SplashRoute)
                // baseConfigViewModel.saveBaseConfigList(proInventoryBaseConfigList)
            },
            modifier = Modifier
                .wrapContentSize(),
            shape = MaterialTheme.shapes.medium
        ) {
            Text(text = stringResource(id = R.string.get_base_config))
        }

      /*  Spacer(modifier = Modifier.weight(1f))
        Text(
            text = DEVICE_ID,
            fontSize = MaterialTheme.typography.bodySmall.fontSize,
        )*/
        Spacer(modifier = Modifier.height(30.dp))

    }
    else {


        BaseConfigList(
            toaster = toaster,
            authViewModel = authViewModel,
            dataViewModel = dataViewModel,
            baseConfigList = baseConfigList
        )


    }
}


@Composable
fun BaseConfigList(
    toaster: ToasterState,
    authViewModel: AuthViewModel,
    dataViewModel: DataViewModel,
    baseConfigList: List<BaseConfig>
) {



    LazyColumn(
        contentPadding = PaddingValues(top = 12.dp, bottom = 12.dp)
    ) {
        items(
            baseConfigList.size,
            key = { position ->
                // Return a stable, unique key for the note
                baseConfigList[position].id_base_config
            }
        ) { position ->
            // specifies text & divider for each item
            Spacer(modifier = Modifier.height(12.dp))

            BaseConfigListItem(
                toaster = toaster,
                position = position,
                dataViewModel = dataViewModel,
                authViewModel = authViewModel,
                baseConfigList = baseConfigList
            )

            Spacer(modifier = Modifier.height(6.dp))
            //  Divider()
        }
    }
}

@Composable
fun BaseConfigListItem(
    toaster: ToasterState,
    position: Int,
    dataViewModel: DataViewModel,
    authViewModel: AuthViewModel,
    baseConfigList: List<BaseConfig>,
) {
    val context = LocalContext.current

    val isActivated = if(baseConfigList[position].licences.isEmpty()) false else {
        baseConfigList[position].licences.any { it.activat == "true" }
    }

    val isProInventory = baseConfigList[position].produit.contains(PRO_INVENTORY)
    val isProInventoryActivated = isProInventory && baseConfigList[position].licences.any { it.activat == "true" &&  it.produit == PRO_INVENTORY}
    val isProCaisse = baseConfigList[position].produit.contains(PRO_CAISSE_MOBILITY)
    val isProCaisseActivated = isProCaisse && baseConfigList[position].licences.any { it.activat == "true" &&  it.produit == PRO_CAISSE_MOBILITY}


    var license = ""
    if (baseConfigList[position].produit.contains(PRO_INVENTORY))
        license = "$PRO_INVENTORY_AUTHORIZATION_TYPE_MENU " + (if(isProInventoryActivated) "" else "(${stringResource(id = R.string.license_not_active)})")



    if (baseConfigList[position].produit.contains(PRO_CAISSE_MOBILITY)){

        license = if(baseConfigList[position].produit.contains(PRO_INVENTORY))
            "$license & $PRO_CAISSE " + (if(isProCaisseActivated) "" else "(${stringResource(id = R.string.license_not_active)})")
        else PRO_CAISSE + (if(isProCaisseActivated) "" else "(${stringResource(id = R.string.license_not_active)})")
    }

    val selectedBaseConfig = authViewModel.selectedIndexBaseConfig
OutlinedCard {
    ItemDetail(
        modifier = Modifier.padding(top = 12.dp, bottom = 12.dp),
        title = baseConfigList[position].designation_base,
        dataText = license,
        tint = if(!isActivated)
            MaterialTheme.colorScheme.error
        else {
            if (!authViewModel.isSameRemoteDB(
                    selectedBase = selectedBaseConfig,
                    base = baseConfigList[position]
                )
            )
                MaterialTheme.colorScheme.tertiary
            else LocalContentColor.current
        },
        icon =
        if(!isActivated)
            Icons.TwoTone.NotInterested
        else {
            if (!authViewModel.isSameRemoteDB(
                    selectedBase = selectedBaseConfig,
                    base = baseConfigList[position]
                )
            )
                Icons.TwoTone.DataObject
            else Icons.TwoTone.Check
        },
        onClick = {
            val selectedBaseConf = authViewModel.selectedIndexBaseConfig
            Log.d("xxBaseConfigListItem", "selectedBaseConfig: $selectedBaseConf")
            if(!isActivated){

                showToast(
                    context = context,
                    toaster = toaster,
                    message = context.resources.getString(R.string.error) + "\n"+ context.resources.getString(R.string.select_base_config),
                    type =  ToastType.Error,
                )
                return@ItemDetail
            }
            if (selectedBaseConf == BaseConfig()) {
                Log.d("xxBaseConfigListItem", "a")
                dataViewModel.saveInventoryActivationState(isProInventoryActivated)
                dataViewModel.saveProcaisseActivationState(isProCaisseActivated)

                dataViewModel.saveIsProInventoryLicenseSelected(isProInventoryActivated)
                authViewModel.onSelectedProInventoryLicense(isProInventoryActivated)

                dataViewModel.saveIsProcaisseLicenseSelected(isProCaisseActivated)
                authViewModel.onSelectedProCaisseLicense(isProCaisseActivated, from = "3")

                dataViewModel.saveSelectedBaseConfig(Json.encodeToString(baseConfigList[position]))
                authViewModel.onSelectedIndexBaseConfigValue(baseConfigList[position])
            }
            else {
                Log.d("xxBaseConfigListItem", "b")
                dataViewModel.saveInventoryActivationState(false)
                dataViewModel.saveProcaisseActivationState(false)

                dataViewModel.saveIsProInventoryLicenseSelected(false)
                authViewModel.onSelectedProInventoryLicense(false)

                dataViewModel.saveIsProcaisseLicenseSelected(false)
                authViewModel.onSelectedProCaisseLicense(false, from = "3")

                dataViewModel.saveSelectedBaseConfig("")
                authViewModel.onSelectedIndexBaseConfigValue(BaseConfig())
            }



        }
    )
}



}
