package com.asmtunis.procaisseinventory.auth.base_config.data.remote.di

import com.asmtunis.procaisseinventory.auth.base_config.data.remote.api.BaseConfigApi
import com.asmtunis.procaisseinventory.auth.base_config.data.remote.api.BaseConfigApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object BaseConfigRemoteModule {
    @Provides
    @Singleton
    fun provideBaseConfigListApi(client: HttpClient): BaseConfigApi = BaseConfigApiImpl(client)


}