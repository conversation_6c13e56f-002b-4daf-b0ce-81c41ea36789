package com.asmtunis.procaisseinventory.auth.base_config.data.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import com.asmtunis.procaisseinventory.auth.spalsh_screen.data.activationservice.domaine.Licence
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.BASECONFIG_TABLE
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Entity(tableName = BASECONFIG_TABLE, primaryKeys = ["id_base_config", "password"])
@Serializable
data class BaseConfig(
    @ColumnInfo(name = "id_base_config")
    var id_base_config: String = "",
    @ColumnInfo(name = "key_base")
    var key_base: String? = "",
    @ColumnInfo(name = "dbName")
    var dbName: String? = "",
    @ColumnInfo(name = "dbIpAddress")
    var dbIpAddress: String? = "",
    @ColumnInfo(name = "adresse_ip")
    var adresse_ip: String = "",
    @ColumnInfo(name = "port")
    var port: String? = "",
    @ColumnInfo(name = "username")
    var username: String? = "",
    @ColumnInfo(name = "password")
    var password: String = "",
    @ColumnInfo(name = "designation_base")
    var designation_base: String = "",
    @ColumnInfo(name = "produit")
    var produit: String = "",
    @ColumnInfo(name = "id_entreprise")
    var id_entreprise: String? = "",
    @ColumnInfo(name = "date_creation")
    var date_creation: String? = "",



    @SerialName("licences")
  // @Transient
    var licences: List<Licence> = emptyList()/* ,
   @ColumnInfo(name = "device_id")
    var device_id: String? = DeviceUtils.getAndroidID(),*/


)
{
    // DeviceUtils.getAndroidID()

    constructor(
         id_base_config: String ,
         key_base: String? ,
         dbName: String? ,
         dbIpAddress: String? ,
         adresse_ip: String ,
         port: String? ,
         username: String? ,
         password: String ,
         designation_base: String ,
         produit: String ,
         id_entreprise: String? ,
         date_creation: String? ,



        ) : this() {
        this.id_base_config = id_base_config
        this.key_base = key_base
        this.dbName = dbName
        this.dbIpAddress = dbIpAddress
        this.adresse_ip = adresse_ip
        this.port = port
        this.username = username
        this.password = password
        this.designation_base = designation_base
        this.produit = produit
        this.date_creation = date_creation
        this.id_entreprise = id_entreprise
    }


    override fun toString(): String {
        return "Connexion{" +
                "idBaseConfig='" + id_base_config + '\'' +
                ", keyBase='" + key_base + '\'' +
                ", dbName='" + dbName + '\'' +
                ", dbIpAddress='" + dbIpAddress + '\'' +
                ", adresseIp='" + adresse_ip + '\'' +
                ", port='" + port + '\'' +
                ", username='" + username + '\'' +
                ", password='" + password + '\'' +
                ", designationBase='" + designation_base + '\'' +
                ", produit='" + produit + '\'' +
                ", idEntreprise='" + id_entreprise + '\'' +
                ", dateCreation='" + date_creation + '\'' +
                '}'
    }
}
