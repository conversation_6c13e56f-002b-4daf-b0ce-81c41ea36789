package com.asmtunis.procaisseinventory.auth.subscribtion.text_validation

import com.asmtunis.countrycodepicker.data.CountryData
import com.asmtunis.countrycodepicker.data.utils.getDefaultLangCode
import com.asmtunis.procaisseinventory.core.UiText
import java.util.Locale

data class SubscribtionFormState(
    val nomsociete: String = "",
    val nomsocieteError: UiText? = null,
    val etablissement: String = "",
    val etablissementError: UiText? = null,
    val email: String = "",
    val emailError: UiText? = null,
  //  val password: String = "",
   // val passwordError: UiText? = null,
    val phone: String = "",
    val phoneError: UiText? = null,

    val onProductSelected: Boolean = false,
    val onProductSelectedError: UiText? = null,

    val countryData: CountryData = CountryData(getDefaultLangCode().lowercase(Locale.getDefault())),
    val subscriptionTime: String = "1 mois"


)
