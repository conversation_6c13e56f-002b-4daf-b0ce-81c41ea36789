package com.asmtunis.procaisseinventory.auth.login.data.remote.api

import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.model.DataResult
import kotlinx.coroutines.flow.Flow


interface LoginApi {


    suspend fun postLogin(baseConfig: String, header :String): Flow<DataResult<Utilisateur>>
    suspend fun getUtilisateurs(baseConfig: String, header :String): Flow<DataResult<List<Utilisateur>>>
    suspend fun insertUserToken(baseConfig: String,token :String): Flow<DataResult<Boolean>>

}