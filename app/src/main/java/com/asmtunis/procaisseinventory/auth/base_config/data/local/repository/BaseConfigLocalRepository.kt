package com.asmtunis.procaisseinventory.auth.base_config.data.local.repository

import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import kotlinx.coroutines.flow.Flow

interface BaseConfigLocalRepository {

    fun upsert(value: BaseConfig) // : Long

    fun upsertAll(value: List<BaseConfig>)
     fun deletebyProduit(value: String)

    // suspend fun  delete(customEntity : CustomEntity)
    fun deletebyid(value: String)

    fun deleteAll()

    fun getAll(): Flow<List<BaseConfig>>

    fun getByTwoID(idbaseconfig: String, password: String): Flow<BaseConfig?>
}
